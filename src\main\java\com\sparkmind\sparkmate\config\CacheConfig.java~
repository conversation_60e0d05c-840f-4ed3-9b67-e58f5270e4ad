package com.sparkmind.sparkmate.config;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class CacheConfig {

    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(caffeineCacheBuilder());
        // Define cache names for specific GET operations
        cacheManager.setCacheNames(java.util.Arrays.asList("fetchDataCache"));
        return cacheManager;
    }

    private Caffeine<Object, Object> caffeineCacheBuilder() {
        return Caffeine.newBuilder()
                .initialCapacity(100) // Initial number of entries
                .maximumSize(500)    // Maximum number of entries
                .expireAfterAccess(10, TimeUnit.MINUTES) // Expire entries after 10 minutes of inactivity
                .recordStats();      // Optional: for cache statistics
    }
}