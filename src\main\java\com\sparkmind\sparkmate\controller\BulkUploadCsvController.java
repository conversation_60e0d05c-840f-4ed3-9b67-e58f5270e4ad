package com.sparkmind.sparkmate.controller;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.UserRecord;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import com.sparkmind.sparkmate.dto.BulkUploadCsvRecord;
import com.sparkmind.sparkmate.dto.BulkUploadResult;
import com.sparkmind.sparkmate.model.*;
import com.sparkmind.sparkmate.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api")
public class BulkUploadCsvController {

    private static final Logger logger = LoggerFactory.getLogger(BulkUploadCsvController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private ProfileInfoService profileInfoService;

    @Autowired
    private ProfileEducationDetailsService educationService;

    @Autowired
    private FamilyDetailsService familyService;

    @Autowired
    private CarrierService carrierService;

    @Autowired
    private LifestyleService lifestyleService;

    @Autowired
    private ContactService contactService;

    @PostMapping(value = "/bulk-upload-csv", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<List<BulkUploadResult>> bulkUploadCsv(@RequestParam("file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            logger.warn("No file uploaded");
            return ResponseEntity.badRequest().body(List.of(
                    new BulkUploadResult("", "", "", false, "No file uploaded", "", "")
            ));
        }

        String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        User currentUser = userService.getUserById(currentUserId)
                .orElseThrow(() -> new RuntimeException("Current user not found"));

        if (!"ADMIN".equals(currentUser.getRole())) {
            logger.warn("Unauthorized bulk upload attempt by userId: {}", currentUserId);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        List<BulkUploadResult> results = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            CsvToBean<BulkUploadCsvRecord> csvToBean = new CsvToBeanBuilder<BulkUploadCsvRecord>(reader)
                    .withType(BulkUploadCsvRecord.class)
                    .withIgnoreLeadingWhiteSpace(true)
                    .build();

            List<BulkUploadCsvRecord> records = csvToBean.parse();
            if (records.isEmpty()) {
                logger.warn("CSV file is empty");
                return ResponseEntity.badRequest().body(List.of(
                        new BulkUploadResult("", "", "", false, "CSV file is empty", "", "")
                ));
            }

            for (BulkUploadCsvRecord record : records) {
                String email = record.getEmail() != null ? record.getEmail() : "";
                String name = record.getName() != null ? record.getName() : "";
                String dob = record.getDob() != null ? record.getDob() : "";

                // Validate required fields
                if (email.isEmpty() || name.isEmpty() || dob.isEmpty()) {
                    logger.warn("Missing required fields for record: email={}, name={}, dob={}", email, name, dob);
                    results.add(new BulkUploadResult(
                            email, "", "", false, "Missing required fields: email, name, or dob", name, ""
                    ));
                    continue;
                }

                try {
                    String password = generatePassword(email, dob);
                    User user = addUser(record, password);
                    String profileId = profileService.getProfileByUserId(user.getId())
                            .orElseGet(() -> profileService.createProfile(user.getId()))
                            .getProfileId();

                    addProfileInfo(profileId, record);
                    addEducation(user.getId(), profileId, record);
                    addFamily(user.getId(), profileId, record);
                    addCarrier(user.getId(), profileId, record);
                    addLifestyle(user.getId(), profileId, record);
                    addContact(user.getId(), profileId, record);

                    results.add(new BulkUploadResult(
                            email, user.getId(), password, true, "", name, profileId
                    ));
                    logger.info("Successfully added record for email: {}", email);
                } catch (Exception e) {
                    logger.error("Failed to add record for email: {} - {}", email, e.getMessage());
                    results.add(new BulkUploadResult(
                            email, "", "", false, e.getMessage(), name, ""
                    ));
                }
            }
        } catch (Exception e) {
            logger.error("Failed to parse CSV file: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(List.of(new BulkUploadResult(
                            "", "", "", false, "Failed to parse CSV: " + e.getMessage(), "", ""
                    )));
        }
        return ResponseEntity.ok(results);
    }

    @PostMapping(value = "/bulk-update-csv", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<List<BulkUploadResult>> bulkUpdateCsv(@RequestParam("file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            logger.warn("No file uploaded");
            return ResponseEntity.badRequest().body(List.of(
                    new BulkUploadResult("", "", "", false, "No file uploaded", "", "")
            ));
        }

        String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        User currentUser = userService.getUserById(currentUserId)
                .orElseThrow(() -> new RuntimeException("Current user not found"));

        if (!"ADMIN".equals(currentUser.getRole()) ) {
            logger.warn("Unauthorized bulk update attempt by userId: {}", currentUserId);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        List<BulkUploadResult> results = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            CsvToBean<BulkUploadCsvRecord> csvToBean = new CsvToBeanBuilder<BulkUploadCsvRecord>(reader)
                    .withType(BulkUploadCsvRecord.class)
                    .withIgnoreLeadingWhiteSpace(true)
                    .build();

            List<BulkUploadCsvRecord> records = csvToBean.parse();
            if (records.isEmpty()) {
                logger.warn("CSV file is empty");
                return ResponseEntity.badRequest().body(List.of(
                        new BulkUploadResult("", "", "", false, "CSV file is empty", "", "")
                ));
            }

            for (BulkUploadCsvRecord record : records) {
                String email = record.getEmail() != null ? record.getEmail() : "";
                String name = record.getName() != null ? record.getName() : "";

                if (email.isEmpty() || name.isEmpty()) {
                    logger.warn("Missing required fields for record: email={}, name={}", email, name);
                    results.add(new BulkUploadResult(
                            email, "", "", false, "Missing required fields: email or name", name, ""
                    ));
                    continue;
                }

                try {
                    User user = updateUser(record);
                    String profileId = profileService.getProfileByUserId(user.getId())
                            .orElseThrow(() -> new RuntimeException("Profile not found for userId: " + user.getId()))
                            .getProfileId();

                    updateProfileInfo(profileId, record);
                    updateEducation(user.getId(), profileId, record);
                    updateFamily(user.getId(), profileId, record);
                    updateCarrier(user.getId(), profileId, record);
                    updateLifestyle(user.getId(), profileId, record);
                    updateContact(user.getId(), profileId, record);

                    results.add(new BulkUploadResult(
                            email, user.getId(), "", true, "", name, profileId
                    ));
                    logger.info("Successfully updated record for email: {}", email);
                } catch (Exception e) {
                    logger.error("Failed to update record for email: {} - {}", email, e.getMessage());
                    results.add(new BulkUploadResult(
                            email, "", "", false, e.getMessage(), name, ""
                    ));
                }
            }
        } catch (Exception e) {
            logger.error("Failed to parse CSV file: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(List.of(new BulkUploadResult(
                            "", "", "", false, "Failed to parse CSV: " + e.getMessage(), "", ""
                    )));
        }
        return ResponseEntity.ok(results);
    }

    private String generatePassword(String email, String dob) {
        if (email == null || email.length() < 5 || dob == null || !dob.matches("\\d{2}-\\d{2}-\\d{4}")) {
            throw new RuntimeException("Invalid email or DOB format");
        }
        String emailPart = email.substring(0, Math.min(email.length(), 5));
        StringBuilder password = new StringBuilder();
        for (int i = 0; i < emailPart.length(); i++) {
            if (i < 4) {
                password.append(Character.toUpperCase(emailPart.charAt(i)));
            } else {
                password.append(Character.toLowerCase(emailPart.charAt(i)));
            }
        }
        String[] dobParts = dob.split("-");
        String ddmm = dobParts[0] + dobParts[1];
        password.append("@").append(ddmm);
        return password.toString();
    }

    private String normalizeMobileNumber(String mobile) {
        if (mobile == null || mobile.trim().isEmpty()) {
            logger.warn("Mobile number is null or empty");
            return null; // Or throw an exception if mobile is required
        }

        // Remove whitespace and special characters, keep digits and '+'
        String cleanedMobile = mobile.replaceAll("[^0-9+]", "").trim();

        // If the number starts with a country code, return it unchanged
        if (cleanedMobile.startsWith("+")) {
            logger.info("Mobile number {} already contains country code", cleanedMobile);
            return cleanedMobile;
        }

        // Validate length for India (10 digits)
        if (cleanedMobile.length() != 10) {
            throw new RuntimeException("Invalid mobile number length for India: " + cleanedMobile + ". Expected 10 digits.");
        }

        // Prepend default country code for India (+91)
        String normalizedMobile = "+91" + cleanedMobile;
        logger.info("Normalized mobile number: {} to {}", cleanedMobile, normalizedMobile);
        return normalizedMobile;
    }

    private User addUser(BulkUploadCsvRecord record, String password) {
        Optional<User> existingUser = userService.findByEmail(record.getEmail());
        if (existingUser.isPresent()) {
            throw new RuntimeException("User with email " + record.getEmail() + " already exists");
        }

        String normalizedMobile;
        try {
            normalizedMobile = normalizeMobileNumber(record.getMobile());
        } catch (Exception e) {
            throw new RuntimeException("Mobile number validation failed: " + e.getMessage());
        }

        String uid;
        try {
            UserRecord.CreateRequest request = new UserRecord.CreateRequest()
                    .setEmail(record.getEmail())
                    .setPassword(password)
                    .setDisplayName(record.getName())
                    .setPhoneNumber(normalizedMobile);

            UserRecord firebaseUser = FirebaseAuth.getInstance().createUser(request);
            uid = firebaseUser.getUid();

            // Send email verification link
            String verificationLink = FirebaseAuth.getInstance().generateEmailVerificationLink(record.getEmail());
            logger.info("Email verification link generated for {}: {}", record.getEmail(), verificationLink);
            // Note: You'll need to send this link via email using your email service (e.g., JavaMail, SendGrid).
        } catch (Exception e) {
            throw new RuntimeException("Failed to create Firebase user or generate verification link: " + e.getMessage());
        }

        User user = new User();
        user.setId(uid);
        user.setName(record.getName());
        user.setEmail(record.getEmail());
        user.setMobile(normalizedMobile);
        user.setRole(record.getRole());
        user.setIsMarriageBureau(true);
        user.setStatus("Active");
        userService.syncMarriageBureauUser(user); // Syncs marriage bureau user to backend database
        return user;
    }

    private User updateUser(BulkUploadCsvRecord record) {
        if (record.getUserId() == null) {
            throw new RuntimeException("userId is required for update operation");
        }
        User user = userService.getUserById(record.getUserId())
                .orElseThrow(() -> new RuntimeException("User not found for userId: " + record.getUserId()));
        if (!record.getEmail().equals(user.getEmail())) {
            throw new RuntimeException("Email cannot be changed during update: " + record.getEmail());
        }

        String normalizedMobile = null;
        if (record.getMobile() != null) {
            try {
                normalizedMobile = normalizeMobileNumber(record.getMobile());
            } catch (Exception e) {
                throw new RuntimeException("Mobile number validation failed: " + e.getMessage());
            }
        }

        user.setName(record.getName() != null ? record.getName() : user.getName());
        user.setMobile(normalizedMobile != null ? normalizedMobile : user.getMobile());
        user.setRole(record.getRole() != null ? record.getRole() : user.getRole());
        user.setIsMarriageBureau(true);
        return userService.updateUser(user.getId(), user);
    }

    private void addProfileInfo(String profileId, BulkUploadCsvRecord record) {
        if (record.getFirstName() != null) {
            Optional<ProfileInfo> existing = profileInfoService.getProfileInfoByProfileId(profileId);
            if (existing.isPresent()) {
                throw new RuntimeException("ProfileInfo already exists for profileId: " + profileId);
            }
            ProfileInfo profileInfo = new ProfileInfo();
            profileInfo.setProfileId(profileId);
            profileInfo.setFirstName(record.getFirstName());
            profileInfo.setLastName(record.getLastName());
            profileInfo.setGender(record.getGender());
            profileInfo.setDob(record.getDob());
            profileInfo.setMaritalStatus(record.getMaritalStatus());
            profileInfo.setReligion(record.getReligion());
            profileInfo.setCaste(record.getCaste());
            profileInfo.setMangal(record.getMangal());
            profileInfo.setZodiacSign(record.getZodiacSign());
            profileInfo.setCountry(record.getCountry());
            profileInfo.setState(record.getState());
            profileInfo.setCity(record.getCity());
            profileInfo.setAboutMe(record.getAboutMe());
            profileInfo.setLookingFor(record.getLookingFor());
            profileInfoService.createProfileInfo(profileId, profileInfo);
        }
    }

    private void updateProfileInfo(String profileId, BulkUploadCsvRecord record) {
        if (record.getFirstName() != null) {
            Optional<ProfileInfo> existing = profileInfoService.getProfileInfoByProfileId(profileId);
            ProfileInfo profileInfo = existing.orElse(new ProfileInfo());
            profileInfo.setProfileId(profileId);
            profileInfo.setFirstName(record.getFirstName() != null ? record.getFirstName() : profileInfo.getFirstName());
            profileInfo.setLastName(record.getLastName() != null ? record.getLastName() : profileInfo.getLastName());
            profileInfo.setGender(record.getGender() != null ? record.getGender() : profileInfo.getGender());
            profileInfo.setDob(record.getDob() != null ? record.getDob() : profileInfo.getDob());
            profileInfo.setMaritalStatus(record.getMaritalStatus() != null ? record.getMaritalStatus() : profileInfo.getMaritalStatus());
            profileInfo.setReligion(record.getReligion() != null ? record.getReligion() : profileInfo.getReligion());
            profileInfo.setCaste(record.getCaste() != null ? record.getCaste() : profileInfo.getCaste());
            profileInfo.setMangal(record.getMangal() != null ? record.getMangal() : profileInfo.getMangal());
            profileInfo.setZodiacSign(record.getZodiacSign() != null ? record.getZodiacSign() : profileInfo.getZodiacSign());
            profileInfo.setCountry(record.getCountry() != null ? record.getCountry() : profileInfo.getCountry());
            profileInfo.setState(record.getState() != null ? record.getState() : profileInfo.getState());
            profileInfo.setCity(record.getCity() != null ? record.getCity() : profileInfo.getCity());
            profileInfo.setAboutMe(record.getAboutMe() != null ? record.getAboutMe() : profileInfo.getAboutMe());
            profileInfo.setLookingFor(record.getLookingFor() != null ? record.getLookingFor() : profileInfo.getLookingFor());
            if (existing.isPresent()) {
                profileInfoService.updateProfileInfo(profileInfo.getId(), profileInfo);
            } else {
                profileInfoService.createProfileInfo(profileId, profileInfo);
            }
        }
    }

    private void addEducation(String userId, String profileId, BulkUploadCsvRecord record) {
        if (record.getSchool() != null || record.getGraduation() != null) {
            Optional<ProfileEducationDetails> existing = educationService.getEducation(userId);
            if (existing.isPresent()) {
                throw new RuntimeException("Education already exists for userId: " + userId);
            }
            ProfileEducationDetails education = new ProfileEducationDetails();
            education.setProfileId(profileId);
            education.setSchool(record.getSchool());
            education.setGraduation(record.getGraduation());
            education.setPostGraduation(record.getPostGraduation());
            education.setUniversity(record.getUniversity());
            educationService.addEducation(userId, education);
        }
    }

    private void updateEducation(String userId, String profileId, BulkUploadCsvRecord record) {
        if (record.getSchool() != null || record.getGraduation() != null) {
            Optional<ProfileEducationDetails> existing = educationService.getEducation(userId);
            ProfileEducationDetails education = existing.orElse(new ProfileEducationDetails());
            education.setProfileId(profileId);
            education.setSchool(record.getSchool() != null ? record.getSchool() : education.getSchool());
            education.setGraduation(record.getGraduation() != null ? record.getGraduation() : education.getGraduation());
            education.setPostGraduation(record.getPostGraduation() != null ? record.getPostGraduation() : education.getPostGraduation());
            education.setUniversity(record.getUniversity() != null ? record.getUniversity() : education.getUniversity());
            if (existing.isPresent()) {
                educationService.updateEducation(userId, education.getId(), education);
            } else {
                educationService.addEducation(userId, education);
            }
        }
    }

    private void addFamily(String userId, String profileId, BulkUploadCsvRecord record) {
        if (record.getFathersName() != null || record.getMothersName() != null) {
            List<FamilyDetails> existing = familyService.getFamilyDetailsByUserId(userId);
            if (!existing.isEmpty()) {
                throw new RuntimeException("Family details already exist for userId: " + userId);
            }
            FamilyDetails family = new FamilyDetails();
            family.setProfileId(profileId);
            family.setFathersName(record.getFathersName());
            family.setMothersName(record.getMothersName());
            family.setBrother(record.getBrother());
            family.setSister(record.getSister());
            family.setFathersOccupation(record.getFathersOccupation());
            family.setMothersOccupation(record.getMothersOccupation());
            family.setFamilyLocation(record.getFamilyLocation());
            family.setFamilyInfo(record.getFamilyInfo());
            familyService.createFamilyDetails(userId, family);
        }
    }

    private void updateFamily(String userId, String profileId, BulkUploadCsvRecord record) {
        if (record.getFathersName() != null || record.getMothersName() != null) {
            List<FamilyDetails> existing = familyService.getFamilyDetailsByUserId(userId);
            FamilyDetails family = existing.isEmpty() ? new FamilyDetails() : existing.get(0);
            family.setProfileId(profileId);
            family.setFathersName(record.getFathersName() != null ? record.getFathersName() : family.getFathersName());
            family.setMothersName(record.getMothersName() != null ? record.getMothersName() : family.getMothersName());
            family.setBrother(record.getBrother() != null ? record.getBrother() : family.getBrother());
            family.setSister(record.getSister() != null ? record.getSister() : family.getSister());
            family.setFathersOccupation(record.getFathersOccupation() != null ? record.getFathersOccupation() : family.getFathersOccupation());
            family.setMothersOccupation(record.getMothersOccupation() != null ? record.getMothersOccupation() : family.getMothersOccupation());
            family.setFamilyLocation(record.getFamilyLocation() != null ? record.getFamilyLocation() : family.getFamilyLocation());
            family.setFamilyInfo(record.getFamilyInfo() != null ? record.getFamilyInfo() : family.getFamilyInfo());
            if (existing.isEmpty()) {
                familyService.createFamilyDetails(userId, family);
            } else {
                familyService.updateFamilyDetails(userId, family.getId(), family);
            }
        }
    }

    private void addCarrier(String userId, String profileId, BulkUploadCsvRecord record) {
        if (record.getOccupation() != null || record.getOrganizationName() != null) {
            Optional<Carrier> existing = carrierService.getCarrierByUserId(userId);
            if (existing.isPresent()) {
                throw new RuntimeException("Carrier already exists for userId: " + userId);
            }
            Carrier carrier = new Carrier();
            carrier.setProfileId(profileId);
            carrier.setOccupation(record.getOccupation());
            carrier.setOrganizationName(record.getOrganizationName());
            carrier.setDepartment(record.getDepartment());
            carrier.setEmployedIn(record.getEmployedIn());
            carrier.setDesignation(record.getDesignation());
            carrier.setEarnings(record.getEarnings());
            carrier.setFarmland(record.getFarmland());
            carrierService.createCarrier(userId, carrier);
        }
    }

    private void updateCarrier(String userId, String profileId, BulkUploadCsvRecord record) {
        if (record.getOccupation() != null || record.getOrganizationName() != null) {
            Optional<Carrier> existing = carrierService.getCarrierByUserId(userId);
            Carrier carrier = existing.orElse(new Carrier());
            carrier.setProfileId(profileId);
            carrier.setOccupation(record.getOccupation() != null ? record.getOccupation() : carrier.getOccupation());
            carrier.setOrganizationName(record.getOrganizationName() != null ? record.getOrganizationName() : carrier.getOrganizationName());
            carrier.setDepartment(record.getDepartment() != null ? record.getDepartment() : carrier.getDepartment());
            carrier.setEmployedIn(record.getEmployedIn() != null ? record.getEmployedIn() : carrier.getEmployedIn());
            carrier.setDesignation(record.getDesignation() != null ? record.getDesignation() : carrier.getDesignation());
            carrier.setEarnings(record.getEarnings() != null ? record.getEarnings() : carrier.getEarnings());
            carrier.setFarmland(record.getFarmland() != null ? record.getFarmland() : carrier.getFarmland());
            if (existing.isPresent()) {
                carrierService.updateCarrier(userId, carrier.getId(), carrier);
            } else {
                carrierService.createCarrier(userId, carrier);
            }
        }
    }

    private void addLifestyle(String userId, String profileId, BulkUploadCsvRecord record) {
        List<String> preferences = record.getLifestylePreferencesList();
        if (preferences != null && !preferences.isEmpty()) {
            List<Lifestyle> existing = lifestyleService.getLifestyleByUserId(userId);
            if (!existing.isEmpty()) {
                throw new RuntimeException("Lifestyle already exists for userId: " + userId);
            }
            Lifestyle lifestyle = new Lifestyle();
            lifestyle.setProfileId(profileId);
            lifestyle.setPreferences(preferences);
            lifestyleService.createLifestyle(userId, lifestyle);
        }
    }

    private void updateLifestyle(String userId, String profileId, BulkUploadCsvRecord record) {
        List<String> preferences = record.getLifestylePreferencesList();
        if (preferences != null && !preferences.isEmpty()) {
            List<Lifestyle> existing = lifestyleService.getLifestyleByUserId(userId);
            Lifestyle lifestyle = existing.isEmpty() ? new Lifestyle() : existing.get(0);
            lifestyle.setProfileId(profileId);
            lifestyle.setPreferences(preferences);
            if (existing.isEmpty()) {
                lifestyleService.createLifestyle(userId, lifestyle);
            } else {
                lifestyleService.updateLifestyle(userId, lifestyle.getId(), lifestyle);
            }
        }
    }

    private void addContact(String userId, String profileId, BulkUploadCsvRecord record) {
        if (record.getPhoneNumber() != null || record.getSecondaryMobileNo() != null) {
            Optional<Contact> existing = contactService.getContactByProfileId(profileId);
            if (existing.isPresent()) {
                throw new RuntimeException("Contact already exists for profileId: " + profileId);
            }
            Contact contact = new Contact();
            contact.setProfileId(profileId);
            contact.setPhoneNumber(record.getPhoneNumber());
            contact.setSecondaryMobileNo(record.getSecondaryMobileNo());
            contact.setInstaId(record.getInstaId());
            contact.setLinkedIn(record.getLinkedIn());
            contact.setAdditionalInfo(record.getAdditionalInfo());
            contactService.createContact(userId, contact);
        }
    }

    private void updateContact(String userId, String profileId, BulkUploadCsvRecord record) {
        if (record.getPhoneNumber() != null || record.getSecondaryMobileNo() != null) {
            Optional<Contact> existing = contactService.getContactByProfileId(profileId);
            Contact contact = existing.orElse(new Contact());
            contact.setProfileId(profileId);
            contact.setPhoneNumber(record.getPhoneNumber() != null ? record.getPhoneNumber() : contact.getPhoneNumber());
            contact.setSecondaryMobileNo(record.getSecondaryMobileNo() != null ? record.getSecondaryMobileNo() : contact.getSecondaryMobileNo());
            contact.setInstaId(record.getInstaId() != null ? record.getInstaId() : contact.getInstaId());
            contact.setLinkedIn(record.getLinkedIn() != null ? record.getLinkedIn() : contact.getLinkedIn());
            contact.setAdditionalInfo(record.getAdditionalInfo() != null ? record.getAdditionalInfo() : contact.getAdditionalInfo());
            if (existing.isPresent()) {
                contactService.updateContact(userId, contact.getId(), contact);
            } else {
                contactService.createContact(userId, contact);
            }
        }
    }
}