package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.Carrier;
import com.sparkmind.sparkmate.service.CarrierService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/carriers")
public class CarrierController {

    private static final Logger logger = LoggerFactory.getLogger(CarrierController.class);

    @Autowired
    private CarrierService carrierService;

    @PostMapping
    public ResponseEntity<Carrier> addCarrier(@Valid @RequestBody Carrier carrier) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Adding carrier for userId: {}", userId);
            Carrier createdCarrier = carrierService.createCarrier(userId, carrier);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdCarrier);
        } catch (IllegalArgumentException e) {
            logger.error("Error adding carrier: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    @GetMapping("/me")
    public ResponseEntity<Carrier> getMyCarrier() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Fetching carrier for userId: {}", userId);
            Optional<Carrier> carrier = carrierService.getCarrierByUserId(userId);
            return carrier.map(c -> ResponseEntity.ok()
                            .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                            .body(c))
                    .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND).body(null));
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching carrier: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    @PutMapping("/{carrierId}")
    public ResponseEntity<Carrier> updateCarrier(
            @PathVariable String carrierId,
            @Valid @RequestBody Carrier updatedCarrier) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Updating carrier id: {} for userId: {}", carrierId, userId);
            Carrier carrier = carrierService.updateCarrier(userId, carrierId, updatedCarrier);
            return ResponseEntity.ok(carrier);
        } catch (RuntimeException e) {
            logger.error("Error updating carrier: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    @DeleteMapping("/{carrierId}")
    public ResponseEntity<Void> deleteCarrier(@PathVariable String carrierId) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Deleting carrier id: {} for userId: {}", carrierId, userId);
            carrierService.deleteCarrier(userId, carrierId);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            logger.error("Error deleting carrier: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }
}