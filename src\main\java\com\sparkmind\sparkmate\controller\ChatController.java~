package com.sparkmind.sparkmate.controller;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.FirebaseToken;
import com.sparkmind.sparkmate.model.ChatMessage;
import com.sparkmind.sparkmate.model.ChatSession;
import com.sparkmind.sparkmate.service.ChatMessageService;
import com.sparkmind.sparkmate.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/chat")
public class ChatController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChatController.class);

    @Autowired
    private ChatMessageService chatMessageService;

    @Autowired
    private UserService userService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    // REST endpoint: Send initial message and return ChatSession
    @PostMapping("/send")
    public ResponseEntity<ChatSession> sendMessage(@RequestBody ChatMessage request) {
        String senderUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        ChatSession session = chatMessageService.sendInitialMessage(
                senderUserId,
                request.getRecipientUserId(),
                request.getContent(),
                request.getMessageType()
        );
        enrichSessionWithUserDetails(session);
        return ResponseEntity.ok(session);
    }

    // REST endpoint: Fetch messages for a session
    @GetMapping("/messages/{sessionId}")
    public ResponseEntity<List<ChatMessage>> getMessages(
            @PathVariable String sessionId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int pageSize) {
        List<ChatMessage> messages = chatMessageService.getMessages(sessionId, page, pageSize);
        return ResponseEntity.ok(messages);
    }

    // REST endpoint: Mark messages as read
    @PutMapping("/messages/{sessionId}/read")
    public ResponseEntity<Void> markMessagesAsRead(@PathVariable String sessionId) {
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        chatMessageService.markMessagesAsRead(sessionId, userId);
        return ResponseEntity.ok().build();
    }

    // WebSocket handler: Process real-time messages
    @MessageMapping("/chat/{sessionId}")
    public void sendWebSocketMessage(
            @DestinationVariable String sessionId,
            @Payload ChatMessage message,
            StompHeaderAccessor accessor) {
        LOGGER.info("Received WebSocket message for sessionId={}: {}", sessionId, message.getContent());

        String authHeader = accessor.getFirstNativeHeader("Authorization");
        LOGGER.info("Authorization header: {}", authHeader);

        if (authHeader == null || !authHeader.toLowerCase().startsWith("bearer ")) {
            LOGGER.error("No valid Bearer token found in headers: {}", authHeader);
            throw new RuntimeException("Missing or invalid Authorization header");
        }

        String idToken = authHeader.substring("bearer ".length()).trim();
        if (idToken.isEmpty()) {
            LOGGER.error("Empty token after extracting from Authorization header");
            throw new RuntimeException("Empty token in Authorization header");
        }

        try {
            FirebaseToken decodedToken = FirebaseAuth.getInstance().verifyIdToken(idToken);
            String uidFromToken = decodedToken.getUid();

            if (!uidFromToken.equals(message.getSenderUserId())) {
                LOGGER.error("SenderId {} does not match token UID {}", message.getSenderUserId(), uidFromToken);
                throw new RuntimeException("SenderId does not match authenticated user");
            }

            String recipientId = message.getRecipientUserId();
            ChatMessage savedMessage = chatMessageService.sendMessage(
                    message.getSenderUserId(),
                    recipientId,
                    message.getContent(),
                    message懒.getMessageType()
            );
            LOGGER.info("WebSocket message processed successfully for sessionId={}", savedMessage.getSessionId());

            messagingTemplate.convertAndSend("/topic/messages/" + savedMessage.getSessionId(), savedMessage);

        } catch (FirebaseAuthException e) {
            LOGGER.error("Token verification failed: {} (Code: {})", e.getMessage(), e.getErrorCode());
            throw new RuntimeException("Invalid token: " + e.getMessage());
        }
    }

    private void enrichSessionWithUserDetails(ChatSession session) {
        userService.getUserById(session.getUser1Id()).ifPresent(user ->
                session.setUser1Name(user.getName() != null ? user.getName() : "Unknown User")
        );
        userService.getUserById(session.getUser2Id()).ifPresent(user ->
                session.setUser2Name(user.getName() != null ? user.getName() : "Unknown User")
        );
    }
}