package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.ChatRequest;
import com.sparkmind.sparkmate.service.ChatRequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/chat-requests")
public class ChatRequestController {

    @Autowired
    private ChatRequestService chatRequestService;

    @PostMapping
    public ResponseEntity<ChatRequest> createChatRequest(@RequestBody ChatRequest request) {
        String requesterUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        ChatRequest createdRequest = chatRequestService.createChatRequest(requesterUserId, request.getRecipientUserId(), request.getMessage());
        return ResponseEntity.status(HttpStatus.CREATED).body(createdRequest);
    }

    @PutMapping("/{requestId}/accept")
    public ResponseEntity<ChatRequest> acceptChatRequest(@PathVariable String requestId) {
        ChatRequest acceptedRequest = chatRequestService.acceptChatRequest(requestId);
        return ResponseEntity.ok(acceptedRequest);
    }

    @PutMapping("/{requestId}/reject")
    public ResponseEntity<ChatRequest> rejectChatRequest(@PathVariable String requestId) {
        ChatRequest rejectedRequest = chatRequestService.rejectChatRequest(requestId);
        return ResponseEntity.ok(rejectedRequest);
    }

    @GetMapping("/pending")
    public ResponseEntity<List<ChatRequest>> getPendingChatRequests() {
        String recipientUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        List<ChatRequest> pendingRequests = chatRequestService.getPendingChatRequests(recipientUserId);
        return ResponseEntity.ok(pendingRequests);
    }

    @GetMapping("/sent")
    public ResponseEntity<List<ChatRequest>> getSentChatRequests(){
        String requesterUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        List<ChatRequest> sentRequests = chatRequestService.getSentChatRequests(requesterUserId);
        return ResponseEntity.ok(sentRequests);
    }
}