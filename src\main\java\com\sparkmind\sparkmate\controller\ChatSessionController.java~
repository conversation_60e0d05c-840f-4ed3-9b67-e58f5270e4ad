package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.ChatSession;
import com.sparkmind.sparkmate.service.ChatSessionService;
import com.sparkmind.sparkmate.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/chat-sessions")
public class ChatSessionController {

    @Autowired
    private ChatSessionService chatSessionService;

    @Autowired
    private UserService userService;

    @GetMapping
    public ResponseEntity<ChatSession> getChatSession(@RequestParam String user2Id) {
        String user1Id = SecurityContextHolder.getContext().getAuthentication().getName();
        ChatSession chatSession = chatSessionService.getOrCreateChatSession(user1Id, user2Id); // Updated method name
        enrichSessionWithUserDetails(chatSession); // Enrich directly since we know session exists or was created
        return ResponseEntity.ok(chatSession); // Always return OK since session is created if not found
    }

    @GetMapping("/conversations")
    public ResponseEntity<List<ChatSession>> getConversations() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            List<ChatSession> conversations = chatSessionService.getConversations(userId);
            conversations.forEach(this::enrichSessionWithUserDetails);
            return ResponseEntity.ok(conversations);
        } catch (Exception e) {
            System.err.println("Error fetching conversations: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/{sessionId}/block/{blockedUserId}")
    public ResponseEntity<ChatSession> blockUserInSession(@PathVariable String sessionId, @PathVariable String blockedUserId) {
        ChatSession updatedSession = chatSessionService.blockUserInSession(sessionId, blockedUserId);
        enrichSessionWithUserDetails(updatedSession);
        return ResponseEntity.ok(updatedSession);
    }

    @PutMapping("/{sessionId}/unblock/{unblockedUserId}")
    public ResponseEntity<ChatSession> unblockUserInSession(@PathVariable String sessionId, @PathVariable String unblockedUserId) {
        ChatSession updatedSession = chatSessionService.unblockUserInSession(sessionId, unblockedUserId);
        enrichSessionWithUserDetails(updatedSession);
        return ResponseEntity.ok(updatedSession);
    }

    private void enrichSessionWithUserDetails(ChatSession session) {
        userService.getUserById(session.getUser1Id()).ifPresent(user ->
                session.setUser1Name(user.getName() != null ? user.getName() : "Unknown User")
        );
        userService.getUserById(session.getUser2Id()).ifPresent(user ->
                session.setUser2Name(user.getName() != null ? user.getName() : "Unknown User")
        );
    }
}