package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.Contact;
import com.sparkmind.sparkmate.service.ContactService;
import com.sparkmind.sparkmate.service.ProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/contacts")
public class ContactController {

    private static final Logger logger = LoggerFactory.getLogger(ContactController.class);

    @Autowired
    private ContactService contactService;

    @Autowired
    private ProfileService profileService;

    @PostMapping
    public ResponseEntity<Contact> addContact(@Valid @RequestBody Contact contact) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Adding contact for userId: {}", userId);
            Contact createdContact = contactService.createContact(userId, contact);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdContact);
        } catch (IllegalArgumentException e) {
            logger.error("Error adding contact: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    @GetMapping("/me")
    public ResponseEntity<Contact> getMyContact() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Fetching contact for userId: {}", userId);
            Optional<Contact> contact = contactService.getContactByProfileId(
                    profileService.getProfileByUserId(userId)
                            .orElseThrow(() -> new IllegalArgumentException("Profile not found"))
                            .getProfileId()
            );
            return contact.map(c -> ResponseEntity.ok()
                            .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                            .body(c))
                    .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND).body(null));
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching contact: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    @PutMapping("/{contactId}")
    public ResponseEntity<Contact> updateContact(
            @PathVariable String contactId,
            @Valid @RequestBody Contact updatedContact) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Updating contact id: {} for userId: {}", contactId, userId);
            Contact updated = contactService.updateContact(userId, contactId, updatedContact);
            return ResponseEntity.ok(updated);
        } catch (IllegalArgumentException e) {
            logger.error("Error updating contact: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }
}