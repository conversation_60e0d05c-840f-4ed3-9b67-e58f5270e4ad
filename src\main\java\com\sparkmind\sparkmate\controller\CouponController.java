package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.Coupon;
import com.sparkmind.sparkmate.service.CouponService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/coupons")
public class CouponController {
    private static final Logger logger = LoggerFactory.getLogger(CouponController.class);

    private final CouponService couponService;

    public CouponController(CouponService couponService) {
        this.couponService = couponService;
    }

    @PostMapping("/create")
    public ResponseEntity<?> createCoupon(@Valid @RequestBody Coupon coupon) {
        try {
            logger.info("Creating coupon: {}", coupon.getCouponCode());
            Coupon createdCoupon = couponService.createCoupon(coupon);
            return ResponseEntity.ok(createdCoupon);
        } catch (RuntimeException e) {
            logger.error("Error creating coupon: {}", e.getMessage());
            return ResponseEntity.status(409).body("Error: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error creating coupon: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Unexpected error: " + e.getMessage());
        }
    }

    @GetMapping("/{couponId}")
    public ResponseEntity<?> getCoupon(@PathVariable String couponId) {
        try {
            logger.info("Fetching coupon: {}", couponId);
            Coupon coupon = couponService.getCouponById(couponId);
            return ResponseEntity.ok(coupon);
        } catch (RuntimeException e) {
            logger.error("Coupon not found: {}", e.getMessage());
            return ResponseEntity.status(404).body("Coupon not found: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error fetching coupon: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Unexpected error: " + e.getMessage());
        }
    }

    @GetMapping
    public ResponseEntity<?> getAllActiveCoupons() {
        try {
            logger.info("Fetching all active coupons");
            List<Coupon> coupons = couponService.getAllActiveCoupons();
            return ResponseEntity.ok(coupons);
        } catch (Exception e) {
            logger.error("Error fetching all coupons: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Error fetching coupons: " + e.getMessage());
        }
    }

    @PutMapping("/{couponId}")
    public ResponseEntity<?> updateCoupon(@PathVariable String couponId, @Valid @RequestBody Coupon coupon) {
        try {
            logger.info("Updating coupon: {}", couponId);
            Coupon updatedCoupon = couponService.updateCoupon(couponId, coupon);
            return ResponseEntity.ok(updatedCoupon);
        } catch (RuntimeException e) {
            logger.error("Error updating coupon: {}", e.getMessage());
            return ResponseEntity.status(404).body("Error: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error updating coupon: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Unexpected error: " + e.getMessage());
        }
    }

    @DeleteMapping("/{couponId}")
    public ResponseEntity<?> deleteCoupon(@PathVariable String couponId) {
        try {
            logger.info("Deleting coupon: {}", couponId);
            couponService.deleteCoupon(couponId);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            logger.error("Error deleting coupon: {}", e.getMessage());
            return ResponseEntity.status(404).body("Coupon not found: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error deleting coupon: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Unexpected error: " + e.getMessage());
        }
    }

    @PostMapping("/apply")
    public ResponseEntity<?> applyCoupon(@RequestParam String couponCode) {
        try {
            logger.info("Applying coupon: {}", couponCode);
            couponService.applyCoupon(couponCode);
            return ResponseEntity.ok("Coupon applied successfully");
        } catch (RuntimeException e) {
            logger.error("Error applying coupon: {}", e.getMessage());
            return ResponseEntity.status(400).body("Error: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error applying coupon: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Unexpected error: " + e.getMessage());
        }
    }
}