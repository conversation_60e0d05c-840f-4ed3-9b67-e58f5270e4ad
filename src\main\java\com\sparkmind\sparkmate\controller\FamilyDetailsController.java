package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.FamilyDetails;
import com.sparkmind.sparkmate.service.FamilyDetailsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/family-details")
public class FamilyDetailsController {

    private static final Logger logger = LoggerFactory.getLogger(FamilyDetailsController.class);

    @Autowired
    private FamilyDetailsService familyDetailsService;

    @PostMapping
    public ResponseEntity<FamilyDetails> addFamilyDetails(@Valid @RequestBody FamilyDetails familyDetails) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.info("Adding family details for userId: {}", userId);
            FamilyDetails createdFamilyDetails = familyDetailsService.createFamilyDetails(userId, familyDetails);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdFamilyDetails);
        } catch (RuntimeException e) {
            logger.error("Error adding family details: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error adding family details: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/me")
    public ResponseEntity<List<FamilyDetails>> getMyFamilyDetails() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.info("Fetching family details for userId: {}", userId);
            List<FamilyDetails> familyDetails = familyDetailsService.getFamilyDetailsByUserId(userId);
            return ResponseEntity.ok(familyDetails);
        } catch (RuntimeException e) {
            logger.error("Error fetching family details: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (Exception e) {
            logger.error("Unexpected error fetching family details: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/{familyDetailsId}")
    public ResponseEntity<FamilyDetails> updateFamilyDetails(
            @PathVariable String familyDetailsId,
            @Valid @RequestBody FamilyDetails updatedFamilyDetails) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.info("Updating family details id: {} for userId: {}", familyDetailsId, userId);
            FamilyDetails familyDetails = familyDetailsService.updateFamilyDetails(userId, familyDetailsId, updatedFamilyDetails);
            return ResponseEntity.ok(familyDetails);
        } catch (RuntimeException e) {
            logger.error("Error updating family details: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error updating family details: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @DeleteMapping("/{familyDetailsId}")
    public ResponseEntity<Void> deleteFamilyDetails(@PathVariable String familyDetailsId) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.info("Deleting family details id: {} for userId: {}", familyDetailsId, userId);
            familyDetailsService.deleteFamilyDetails(userId, familyDetailsId);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            logger.error("Error deleting family details: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (Exception e) {
            logger.error("Unexpected error deleting family details: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}