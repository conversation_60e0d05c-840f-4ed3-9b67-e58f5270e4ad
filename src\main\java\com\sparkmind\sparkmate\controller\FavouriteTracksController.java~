package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.dto.FavouriteProfileDTO;
import com.sparkmind.sparkmate.dto.FavouriteRequestDTO;
import com.sparkmind.sparkmate.model.FavouriteTracks;
import com.sparkmind.sparkmate.service.FavouriteTracksService;
import com.sparkmind.sparkmate.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/favorites")
public class FavouriteTracksController {

    private static final Logger logger = LoggerFactory.getLogger(FavouriteTracksController.class);

    @Autowired
    private FavouriteTracksService favouriteTracksService;

    @Autowired
    private NotificationService notificationService;

    @PostMapping("/add")
    public ResponseEntity<?> addFavorite(@RequestBody FavouriteRequestDTO favouriteRequest) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            if (!userId.equals(favouriteRequest.getSenderUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body("You can only add favorites for your own user ID");
            }
            if (favouriteRequest.getReceiverProfileId() == null) {
                return ResponseEntity.badRequest().body("Missing receiverProfileId");
            }

            logger.debug("Adding favorite: senderUserId {} -> receiverProfileId {}", favouriteRequest.getSenderUserId(), favouriteRequest.getReceiverProfileId());
            FavouriteTracks savedFavourite = favouriteTracksService.addFavourite(
                    favouriteRequest.getSenderUserId(),
                    favouriteRequest.getReceiverProfileId()
            );

            notificationService.createNotification(
                    favouriteRequest.getSenderUserId(),
                    favouriteRequest.getReceiverProfileId()
            );

            return ResponseEntity.status(HttpStatus.CREATED).body(savedFavourite);
        } catch (IllegalArgumentException e) {
            logger.error("Error adding favorite: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PostMapping("/remove")
    public ResponseEntity<?> removeFavorite(@RequestBody FavouriteRequestDTO favouriteRequest) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            if (!userId.equals(favouriteRequest.getSenderUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body("You can only remove favorites for your own user ID");
            }
            if (favouriteRequest.getReceiverProfileId() == null) {
                return ResponseEntity.badRequest().body("Missing receiverProfileId");
            }

            logger.debug("Removing favorite: senderUserId {} -> receiverProfileId {}", favouriteRequest.getSenderUserId(), favouriteRequest.getReceiverProfileId());
            favouriteTracksService.removeFavourite(favouriteRequest.getSenderUserId(), favouriteRequest.getReceiverProfileId());
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            logger.error("Error removing favorite: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        }
    }

    @GetMapping("/me")
    public ResponseEntity<List<FavouriteProfileDTO>> getMyFavorites() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Fetching favorites for userId: {}", userId);
            List<FavouriteProfileDTO> favourites = favouriteTracksService.getAllFavourites(userId);
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                    .body(favourites);
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching favorites: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }
}