package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.dto.FavouriteProfileDTO;
import com.sparkmind.sparkmate.dto.InterestDTO;
import com.sparkmind.sparkmate.dto.ProfileInterestDTO;
import com.sparkmind.sparkmate.model.InterestTrack;
import com.sparkmind.sparkmate.service.InterestTrackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/interests")
public class InterestTrackController {

    private static final Logger logger = LoggerFactory.getLogger(InterestTrackController.class);

    @Autowired
    private InterestTrackService service;

    @PostMapping("/send")
    public ResponseEntity<?> sendInterest(@RequestBody InterestDTO interestDTO) {
        try {
            if (interestDTO.getSenderUserId() == null || interestDTO.getReceiverProfileId() == null) {
                return ResponseEntity.badRequest().body("Sender user ID and receiver profile ID are required");
            }

            String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
            if (!currentUserId.equals(interestDTO.getSenderUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body("You can only send interest from your own user ID");
            }

            InterestTrack result = service.sendInterest(interestDTO.getSenderUserId(),
                    interestDTO.getReceiverProfileId());
            return ResponseEntity.status(HttpStatus.CREATED).body(result);
        } catch (IllegalArgumentException e) {
            logger.error("Error sending interest: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error sending interest: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred");
        }
    }

    @GetMapping("/sent")
    public ResponseEntity<List<FavouriteProfileDTO>> getMySentInterests() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            List<FavouriteProfileDTO> interests = service.getSentInterests(userId);
            return ResponseEntity.ok(interests);
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching sent interests: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error fetching sent interests: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/received")
    public ResponseEntity<List<FavouriteProfileDTO>> getMyReceivedInterests() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            List<FavouriteProfileDTO> interests = service.getReceivedInterests(userId);
            return ResponseEntity.ok(interests);
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching received interests: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error fetching received interests: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    @DeleteMapping("/remove")
    public ResponseEntity<?> removeInterest(@RequestBody InterestDTO interestDTO) {
        try {
            if (interestDTO.getSenderUserId() == null || interestDTO.getReceiverProfileId() == null) {
                return ResponseEntity.badRequest().body("Sender user ID and receiver profile ID are required");
            }

            String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
            if (!currentUserId.equals(interestDTO.getSenderUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body("You can only remove your own interest");
            }

            service.removeInterest(interestDTO.getSenderUserId(), interestDTO.getReceiverProfileId());
            return ResponseEntity.ok().body("Interest removed successfully");
        } catch (IllegalArgumentException e) {
            logger.error("Error removing interest: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error removing interest: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred");
        }
    }

    @GetMapping("/most")
    public ResponseEntity<ProfileInterestDTO> mostInterested() {
        try {
            ProfileInterestDTO result = service.mostInterested();
            return result != null ? ResponseEntity.ok(result) : ResponseEntity.noContent().build();
        } catch (Exception e) {
            logger.error("Unexpected error fetching most interested: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/least")
    public ResponseEntity<ProfileInterestDTO> leastInterested() {
        try {
            ProfileInterestDTO result = service.leastInterested();
            return result != null ? ResponseEntity.ok(result) : ResponseEntity.noContent().build();
        } catch (Exception e) {
            logger.error("Unexpected error fetching least interested: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
}