package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.dto.FavouriteProfileDTO;
import com.sparkmind.sparkmate.dto.InterestDTO;
import com.sparkmind.sparkmate.dto.ProfileInterestDTO;
import com.sparkmind.sparkmate.model.InterestTrack;
import com.sparkmind.sparkmate.service.InterestTrackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/interests")
public class InterestTrackController {

    private static final Logger logger = LoggerFactory.getLogger(InterestTrackController.class);

    @Autowired
    private InterestTrackService service;

    @PostMapping("/send")
    public ResponseEntity<?> sendInterest(@RequestBody InterestDTO interestDTO) {
        try {
            if (interestDTO.getSenderUserId() == null || interestDTO.getReceiverProfileId() == null) {
                return ResponseEntity.badRequest().body("Sender user ID and receiver profile ID are required");
            }

            String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
            if (!currentUserId.equals(interestDTO.getSenderUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body("You can only send interest from your own user ID");
            }

            logger.debug("Sending interest from {} to {}", interestDTO.getSenderUserId(), interestDTO.getReceiverProfileId());
            InterestTrack result = service.sendInterest(interestDTO.getSenderUserId(),
                    interestDTO.getReceiverProfileId());
            return ResponseEntity.status(HttpStatus.CREATED).body(result);
        } catch (IllegalArgumentException e) {
            logger.error("Error sending interest: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/sent")
    public ResponseEntity<List<FavouriteProfileDTO>> getMySentInterests() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Fetching sent interests for userId: {}", userId);
            List<FavouriteProfileDTO> interests = service.getSentInterests(userId);
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                    .body(interests);
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching sent interests: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    @GetMapping("/received")
    public ResponseEntity<List<FavouriteProfileDTO>> getMyReceivedInterests() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Fetching received interests for userId: {}", userId);
            List<FavouriteProfileDTO> interests = service.getReceivedInterests(userId);
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                    .body(interests);
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching received interests: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    @DeleteMapping("/remove")
    public ResponseEntity<?> removeInterest(@RequestBody InterestDTO interestDTO) {
        try {
            if (interestDTO.getSenderUserId() == null || interestDTO.getReceiverProfileId() == null) {
                return ResponseEntity.badRequest().body("Sender user ID and receiver profile ID are required");
            }

            String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
            if (!currentUserId.equals(interestDTO.getSenderUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body("You can only remove your own interest");
            }

            logger.debug("Removing interest from {} to {}", interestDTO.getSenderUserId(), interestDTO.getReceiverProfileId());
            service.removeInterest(interestDTO.getSenderUserId(), interestDTO.getReceiverProfileId());
            return ResponseEntity.ok().body("Interest removed successfully");
        } catch (IllegalArgumentException e) {
            logger.error("Error removing interest: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/most")
    public ResponseEntity<ProfileInterestDTO> mostInterested() {
        try {
            logger.debug("Fetching most interested profile");
            ProfileInterestDTO result = service.mostInterested();
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(10, TimeUnit.MINUTES))
                    .body(result);
        } catch (Exception e) {
            logger.error("Unexpected error fetching most interested: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/least")
    public ResponseEntity<ProfileInterestDTO> leastInterested() {
        try {
            logger.debug("Fetching least interested profile");
            ProfileInterestDTO result = service.leastInterested();
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(10, TimeUnit.MINUTES))
                    .body(result);
        } catch (Exception e) {
            logger.error("Unexpected error fetching least interested: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
}