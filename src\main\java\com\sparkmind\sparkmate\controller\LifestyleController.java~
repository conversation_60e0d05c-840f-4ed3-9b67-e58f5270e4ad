package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.Lifestyle;
import com.sparkmind.sparkmate.service.LifestyleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/lifestyle")
public class LifestyleController {

    private static final Logger logger = LoggerFactory.getLogger(LifestyleController.class);

    @Autowired
    private LifestyleService lifestyleService;

    @PostMapping
    public ResponseEntity<Lifestyle> addLifestyle(@Valid @RequestBody Lifestyle lifestyle) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Adding lifestyle for userId: {}", userId);
            Lifestyle createdLifestyle = lifestyleService.createLifestyle(userId, lifestyle);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdLifestyle);
        } catch (RuntimeException e) {
            logger.error("Error adding lifestyle: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    @GetMapping("/me")
    public ResponseEntity<List<Lifestyle>> getMyLifestyle() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Fetching lifestyle for userId: {}", userId);
            List<Lifestyle> lifestyles = lifestyleService.getLifestyleByUserId(userId);
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                    .body(lifestyles);
        } catch (RuntimeException e) {
            logger.error("Error fetching lifestyle: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @PutMapping("/{lifestyleId}")
    public ResponseEntity<Lifestyle> updateLifestyle(
            @PathVariable String lifestyleId,
            @Valid @RequestBody Lifestyle updatedLifestyle) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Updating lifestyle id: {} for userId: {}", lifestyleId, userId);
            Lifestyle lifestyle = lifestyleService.updateLifestyle(userId, lifestyleId, updatedLifestyle);
            return ResponseEntity.ok(lifestyle);
        } catch (RuntimeException e) {
            logger.error("Error updating lifestyle: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    @DeleteMapping("/{lifestyleId}")
    public ResponseEntity<Void> deleteLifestyle(@PathVariable String lifestyleId) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Deleting lifestyle id: {} for userId: {}", lifestyleId, userId);
            lifestyleService.deleteLifestyle(userId, lifestyleId);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            logger.error("Error deleting lifestyle: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }
}