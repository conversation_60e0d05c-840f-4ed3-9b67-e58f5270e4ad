package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.ProfileImages;
import com.sparkmind.sparkmate.model.User;
import com.sparkmind.sparkmate.service.ProfileImagesService;
import com.sparkmind.sparkmate.service.ProfileService;
import com.sparkmind.sparkmate.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/marriage-bureau/profile-images")
public class MarriageBureauProfileImageController {

    private static final Logger logger = LoggerFactory.getLogger(MarriageBureauProfileImageController.class);

    private final ProfileImagesService profileImagesService;
    private final ProfileService profileService;
    private final UserService userService;

    public MarriageBureauProfileImageController(
            ProfileImagesService profileImagesService,
            ProfileService profileService,
            UserService userService) {
        this.profileImagesService = profileImagesService;
        this.profileService = profileService;
        this.userService = userService;
    }

    // Authorization check for Marriage Bureau or Admin
    private ResponseEntity<?> authorizeRequest(User currentUser, String targetUserId) {
        String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        if (!"ADMIN".equals(currentUser.getRole()) && !Boolean.TRUE.equals(currentUser.getIsMarriageBureau())) {
            logger.warn("Unauthorized attempt by userId: {} to access Marriage Bureau endpoint", currentUserId);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body("Only Admin or Marriage Bureau users can perform this action");
        }
        return null; // Authorized
    }

    // POST: Upload images for a specific user (Admin or Marriage Bureau only)
    @PostMapping(value = "/{userId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> uploadMarriageBureauProfileImages(
            @PathVariable String userId,
            @RequestParam("images") MultipartFile[] files) {
        try {
            String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
            User currentUser = userService.getUserById(currentUserId)
                    .orElseThrow(() -> new IllegalArgumentException("Current user not found: " + currentUserId));

            ResponseEntity<?> authResponse = authorizeRequest(currentUser, userId);
            if (authResponse != null) return authResponse;

            User targetUser = userService.getUserById(userId)
                    .orElseThrow(() -> new IllegalArgumentException("Target user not found: " + userId));

            logger.info("Uploading {} images for userId: {} by currentUserId: {}", files.length, userId, currentUserId);
            ProfileImages savedImages = profileImagesService.uploadOrUpdateProfileImages(userId, files);
            return ResponseEntity.status(HttpStatus.CREATED).body(savedImages);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid request uploading images for userId: {}: {}", userId, e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (IOException e) {
            logger.error("IO error uploading images for userId: {}: {}", userId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to upload images to storage: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error uploading images for userId: {}: {}", userId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Unexpected error: " + e.getMessage());
        }
    }

    // GET: Retrieve images for a specific user's profile (Admin or Marriage Bureau only)
    @GetMapping("/{userId}")
    public ResponseEntity<?> getMarriageBureauProfileImages(@PathVariable String userId) {
        try {
            String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
            User currentUser = userService.getUserById(currentUserId)
                    .orElseThrow(() -> new IllegalArgumentException("Current user not found: " + currentUserId));

            ResponseEntity<?> authResponse = authorizeRequest(currentUser, userId);
            if (authResponse != null) return authResponse;

            User targetUser = userService.getUserById(userId)
                    .orElseThrow(() -> new IllegalArgumentException("Target user not found: " + userId));

            String profileId = profileService.getProfileByUserId(userId)
                    .orElseThrow(() -> new IllegalArgumentException("Profile not found for user: " + userId))
                    .getProfileId();

            logger.info("Fetching images for userId: {} by currentUserId: {}", userId, currentUserId);
            ProfileImages images = profileImagesService.retrieveProfileImages(profileId);
            if (images == null) {
                logger.debug("No images found for userId: {}, returning empty object", userId);
                return ResponseEntity.ok(new ProfileImages());
            }
            return ResponseEntity.ok(images);
        } catch (IllegalArgumentException e) {
            logger.error("Error retrieving images for userId: {}: {}", userId, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error retrieving images for userId: {}: {}", userId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Unexpected error: " + e.getMessage());
        }
    }

    // DELETE: Remove images for a specific user's profile (Admin or Marriage Bureau only)
    @DeleteMapping("/{userId}")
    public ResponseEntity<?> removeMarriageBureauProfileImages(@PathVariable String userId) {
        try {
            String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
            User currentUser = userService.getUserById(currentUserId)
                    .orElseThrow(() -> new IllegalArgumentException("Current user not found: " + currentUserId));

            ResponseEntity<?> authResponse = authorizeRequest(currentUser, userId);
            if (authResponse != null) return authResponse;

            User targetUser = userService.getUserById(userId)
                    .orElseThrow(() -> new IllegalArgumentException("Target user not found: " + userId));

            logger.info("Removing images for userId: {} by currentUserId: {}", userId, currentUserId);
            profileImagesService.removeProfileImages(userId);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            logger.error("Error removing images for userId: {}: {}", userId, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error removing images for userId: {}: {}", userId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Unexpected error: " + e.getMessage());
        }
    }

    // GET: Fetch all users where isMarriageBureau = true (Admin or Marriage Bureau only)
    @GetMapping("/marriage-bureau-users")
    public ResponseEntity<?> getAllMarriageBureauUsers() {
        try {
            String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
            User currentUser = userService.getUserById(currentUserId)
                    .orElseThrow(() -> new IllegalArgumentException("Current user not found: " + currentUserId));

            ResponseEntity<?> authResponse = authorizeRequest(currentUser, null);
            if (authResponse != null) return authResponse;

            logger.info("Fetching all Marriage Bureau users by currentUserId: {}", currentUserId);
            List<User> marriageBureauUsers = userService.getAllUsers().stream()
                    .filter(user -> Boolean.TRUE.equals(user.getIsMarriageBureau()))
                    .collect(Collectors.toList());

            return ResponseEntity.ok(marriageBureauUsers);
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching Marriage Bureau users: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error fetching Marriage Bureau users: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Unexpected error: " + e.getMessage());
        }
    }
}