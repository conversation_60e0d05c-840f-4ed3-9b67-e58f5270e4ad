package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.Notification;
import com.sparkmind.sparkmate.repository.NotificationRepository;
import com.sparkmind.sparkmate.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/notifications")
public class NotificationController {

    private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private NotificationRepository notificationRepository;

    @PostMapping
    public ResponseEntity<Notification> createNotification(
            @RequestParam String senderUserId,
            @RequestParam String receiverProfileId) {
        try {
            String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
            if (!currentUserId.equals(senderUserId)) {
                logger.warn("Unauthorized attempt to create notification by user: {}", currentUserId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }

            logger.debug("Creating notification from {} to profile {}", senderUserId, receiverProfileId);
            Notification notification = notificationService.createNotification(senderUserId, receiverProfileId);
            return ResponseEntity.status(HttpStatus.CREATED).body(notification);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid request: {}", e.getMessage());
            return ResponseEntity.badRequest().body(null);
        }
    }

    @PutMapping("/{id}/read")
    public ResponseEntity<Void> markNotificationAsRead(@PathVariable String id) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            Optional<Notification> notificationOpt = notificationRepository.findById(id);

            if (notificationOpt.isEmpty() || !notificationOpt.get().getReceiverUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            Notification notification = notificationOpt.get();
            if (!notification.isRead()) {
                notification.setRead(true);
                notificationRepository.save(notification);
                logger.info("Notification {} marked as read for user {}", id, userId);
                // Evict caches for this user
                notificationService.getAllNotifications(userId); // Trigger cache refresh
                notificationService.getRecentNotifications(userId); // Trigger cache refresh
            }

            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error marking notification as read: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/all")
    public ResponseEntity<List<Notification>> getAllNotifications() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Fetching all notifications for user: {}", userId);
            List<Notification> notifications = notificationService.getAllNotifications(userId);
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                    .body(notifications);
        } catch (Exception e) {
            logger.error("Error fetching all notifications: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/recent")
    public ResponseEntity<List<Notification>> getRecentNotifications() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Fetching recent notifications for user: {}", userId);
            List<Notification> notifications = notificationService.getRecentNotifications(userId);
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                    .body(notifications);
        } catch (Exception e) {
            logger.error("Error fetching recent notifications: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
}