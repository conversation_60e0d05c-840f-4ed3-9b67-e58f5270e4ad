package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.dto.FavouriteProfileDTO;
import com.sparkmind.sparkmate.dto.ProfileCardDTO;
import com.sparkmind.sparkmate.model.Carrier;
import com.sparkmind.sparkmate.model.Lifestyle;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.ProfileImages;
import com.sparkmind.sparkmate.model.ProfileInfo;
import com.sparkmind.sparkmate.service.CarrierService;
import com.sparkmind.sparkmate.service.FavouriteTracksService;
import com.sparkmind.sparkmate.service.InterestTrackService;
import com.sparkmind.sparkmate.service.LifestyleService;
import com.sparkmind.sparkmate.service.ProfileImagesService;
import com.sparkmind.sparkmate.service.ProfileInfoService;
import com.sparkmind.sparkmate.service.ProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/profiles")
public class ProfileBulkController {

    private static final Logger logger = LoggerFactory.getLogger(ProfileBulkController.class);

    @Autowired
    private ProfileService profileService;

    @Autowired
    private FavouriteTracksService favouriteTracksService;

    @Autowired
    private InterestTrackService interestTrackService;

    @Autowired
    private LifestyleService lifestyleService;

    @Autowired
    private ProfileInfoService profileInfoService;

    @Autowired
    private CarrierService carrierService;

    @Autowired
    private ProfileImagesService profileImagesService;

    @GetMapping("/{userId}")
    public ResponseEntity<ProfileCardDTO> getProfileCardData(@PathVariable String userId) {
        try {
            String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.info("Fetching profile card data for userId: {} by user: {}", userId, currentUserId);

            // Fetch the profile
            Profile profile = profileService.getProfileByUserId(userId)
                    .orElseThrow(() -> new IllegalArgumentException("Profile not found for userId: " + userId));

            // Fetch favorites and interests for the current user
            List<FavouriteProfileDTO> favorites = favouriteTracksService.getAllFavourites(currentUserId);
            List<FavouriteProfileDTO> interests = interestTrackService.getSentInterests(currentUserId);
            List<String> favoriteProfileIds = favorites.stream()
                    .map(FavouriteProfileDTO::getProfileId)
                    .collect(Collectors.toList());
            List<String> interestProfileIds = interests.stream()
                    .map(FavouriteProfileDTO::getProfileId)
                    .collect(Collectors.toList());

            // Fetch lifestyle preferences with explicit typing
            List<Lifestyle> lifestyles = lifestyleService.getLifestyleByProfileIds(Collections.singletonList(profile.getProfileId()));
            List<String> lifestylePreferences = (List<String>) lifestyles.stream()
                    .filter(l -> l.getProfileId().equals(profile.getProfileId()))
                    .findFirst()
                    .map(lifestyle -> lifestyle.getPreferences() != null ?
                            lifestyle.getPreferences().stream()
                                    .map(Object::toString) // Ensure each preference is a String
                                    .limit(3)
                                    .collect(Collectors.toList()) :
                            Collections.emptyList())
                    .orElse(Collections.emptyList());

            // Fetch additional profile details
            ProfileInfo profileInfo = profileInfoService.getProfileInfoByUserId(userId).orElse(null);
            Carrier carrier = carrierService.getCarrierByUserId(userId).orElse(null);
            ProfileImages profileImages = profileImagesService.retrieveProfileImages(profile.getProfileId());

            // Build ProfileCardDTO
            FavouriteProfileDTO favDto = favorites.stream()
                    .filter(f -> f.getProfileId().equals(profile.getProfileId()))
                    .findFirst()
                    .orElse(null);

            ProfileCardDTO dto = new ProfileCardDTO();
            dto.setUserId(profile.getUserId());
            dto.setProfileId(profile.getProfileId());
            dto.setFirstName(favDto != null ? favDto.getFirstName() : (profileInfo != null ? profileInfo.getFirstName() : "Unknown"));
            dto.setAge(favDto != null ? favDto.getAge() : (profileInfo != null ? profileInfo.getAge() : null));
            dto.setDesignation(favDto != null ? favDto.getDesignation() : (carrier != null ? carrier.getDesignation() : null));
            dto.setProfileImage(favDto != null ? favDto.getProfileImage() : (profileImages != null ? profileImages.getPrimaryImage() : null));
            dto.setLifestylePreferences(lifestylePreferences);
            dto.setFavorited(favoriteProfileIds.contains(profile.getProfileId()));
            dto.setInterested(interestProfileIds.contains(profile.getProfileId()));
            if (profileInfo != null) {
                String location = buildLocation(profileInfo.getCountry(), profileInfo.getState(), profileInfo.getCity());
                dto.setLocation(location);
            }
            return ResponseEntity.ok(dto);
        } catch (IllegalArgumentException e) {
            logger.warn("Profile not found for userId: {}", userId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (Exception e) {
            logger.error("Error fetching profile card data for userId {}: {}", userId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private String buildLocation(String country, String state, String city) {
        StringBuilder location = new StringBuilder();

        if (country != null && !country.trim().isEmpty()) {
            location.append(country.trim());
        }

        if (state != null && !state.trim().isEmpty()) {
            if (location.length() > 0) location.append(", ");
            location.append(state.trim());
        }

        if (city != null && !city.trim().isEmpty()) {
            if (location.length() > 0) location.append(", ");
            location.append(city.trim());
        }

        return location.length() > 0 ? location.toString() : null;
    }
}