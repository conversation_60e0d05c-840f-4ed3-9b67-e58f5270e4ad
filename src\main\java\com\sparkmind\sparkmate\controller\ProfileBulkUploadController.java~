package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.*;
import com.sparkmind.sparkmate.service.*;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/api")
public class ProfileBulkUploadController {

    private static final Logger logger = LoggerFactory.getLogger(ProfileBulkUploadController.class);

    @Autowired
    private UserService userService;
    @Autowired
    private ProfileService profileService;
    @Autowired
    private ProfileInfoService profileInfoService;
    @Autowired
    private ProfileEducationDetailsService educationService;
    @Autowired
    private LifestyleService lifestyleService;
    @Autowired
    private FamilyDetailsService familyDetailsService;
    @Autowired
    private ContactService contactService;
    @Autowired
    private CarrierService carrierService;

    @PostMapping("/profile-bulk-upload")
    public ResponseEntity<BulkUploadResponse> bulkUploadProfiles(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body(new BulkUploadResponse(0, List.of("No file uploaded")));
        }

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()));
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {

            List<String> errors = new ArrayList<>();
            int successCount = 0;

            for (CSVRecord record : csvParser) {
                try {
                    validateRequiredFields(record);
                    processProfileRecord(record);
                    successCount++;
                } catch (Exception e) {
                    String errorMsg = "Error in row " + record.getRecordNumber() + ": " + e.getMessage();
                    logger.error(errorMsg);
                    errors.add(errorMsg);
                }
            }

            BulkUploadResponse response = new BulkUploadResponse(successCount, errors);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to process CSV file: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new BulkUploadResponse(0, List.of("Failed to process file: " + e.getMessage())));
        }
    }

    private void validateRequiredFields(CSVRecord record) {
        List<String> missingFields = new ArrayList<>();
        if (!record.isSet("email") || record.get("email").isEmpty()) missingFields.add("email");
        if (!record.isSet("name") || record.get("name").isEmpty()) missingFields.add("name");
        if (!record.isSet("role") || record.get("role").isEmpty()) missingFields.add("role");
        if (!record.isSet("status") || record.get("status").isEmpty()) missingFields.add("status");
        if (!record.isSet("firstName") || record.get("firstName").isEmpty()) missingFields.add("firstName");
        if (!record.isSet("gender") || record.get("gender").isEmpty()) missingFields.add("gender");
        if (!record.isSet("dob") || record.get("dob").isEmpty()) missingFields.add("dob");

        if (!missingFields.isEmpty()) {
            throw new IllegalArgumentException("Missing required fields: " + String.join(", ", missingFields));
        }
    }

    private void processProfileRecord(CSVRecord record) {
        // User
        User user = userService.findByEmail(record.get("email")).orElseGet(() -> {
            User newUser = new User();
            newUser.setId(record.isSet("userId") && !record.get("userId").isEmpty() ? record.get("userId") : null);
            newUser.setEmail(record.get("email"));
            newUser.setName(record.get("name"));
            newUser.setMobile(record.get("mobile"));
            newUser.setLookingFor(record.get("lookingFor"));
            newUser.setRole(record.get("role"));
            newUser.setStatus(record.get("status"));
            newUser.setAdditionalInfo(record.get("additionalInfo"));
            return userService.createUser(newUser);
        });

        // Profile (profileId is auto-generated)
        Profile profile = profileService.getProfileByUserId(user.getId()).orElseGet(() -> profileService.createProfile(user.getId()));
        if (record.isSet("lastSeen") && !record.get("lastSeen").isEmpty()) {
            profile.setLastSeen(LocalDateTime.parse(record.get("lastSeen")));
        }
        profile.setActive(record.isSet("active") ? Boolean.parseBoolean(record.get("active")) : true);
        profileService.updateProfile(profile.getProfileId(), profile);

        // ProfileInfo
        ProfileInfo profileInfo = profileInfoService.getProfileInfoByProfileId(profile.getProfileId()).orElse(new ProfileInfo());
        profileInfo.setProfileId(profile.getProfileId()); // Use auto-generated profileId
        profileInfo.setFirstName(record.get("firstName"));
        profileInfo.setLastName(record.get("lastName"));
        profileInfo.setGender(record.get("gender"));
        profileInfo.setDob(record.get("dob"));
        profileInfo.setMaritalStatus(record.get("maritalStatus"));
        profileInfo.setReligion(record.isSet("religion") && !record.get("religion").isEmpty() ? record.get("religion") : "Hindu");
        profileInfo.setCaste(record.get("caste"));
        profileInfo.setMangal(record.isSet("mangal") ? Boolean.parseBoolean(record.get("mangal")) : null);
        profileInfo.setZodiacSign(record.get("zodiacSign"));
        profileInfo.setCountry(record.isSet("country") && !record.get("country").isEmpty() ? record.get("country") : "India");
        profileInfo.setState(record.get("state"));
        profileInfo.setCity(record.get("city"));
        profileInfo.setAboutMe(record.get("aboutMe"));
        profileInfo.setLookingFor(record.get("lookingForProfile"));
        if (profileInfo.getId() == null) {
            profileInfoService.createProfileInfo(profile.getProfileId(), profileInfo);
        } else {
            profileInfoService.updateProfileInfo(profileInfo.getId(), profileInfo);
        }

        // ProfileEducationDetails
        if (record.isSet("school") || record.isSet("graduation") || record.isSet("postGraduation") || record.isSet("university")) {
            ProfileEducationDetails education = educationService.getEducation(user.getId()).orElse(new ProfileEducationDetails());
            education.setProfileId(profile.getProfileId()); // Use auto-generated profileId
            education.setSchool(record.get("school"));
            education.setGraduation(record.get("graduation"));
            education.setPostGraduation(record.get("postGraduation"));
            education.setUniversity(record.get("university"));
            if (education.getId() == null) {
                educationService.addEducation(user.getId(), education);
            } else {
                educationService.updateEducation(user.getId(), education.getId(), education);
            }
        }

        // Lifestyle
        if (record.isSet("preferences") && !record.get("preferences").isEmpty()) {
            Lifestyle lifestyle = lifestyleService.getLifestyleByUserId(user.getId()).stream().findFirst().orElse(new Lifestyle());
            lifestyle.setProfileId(profile.getProfileId()); // Use auto-generated profileId
            lifestyle.setPreferences(Arrays.asList(record.get("preferences").split(",")));
            if (lifestyle.getId() == null) {
                lifestyleService.createLifestyle(user.getId(), lifestyle);
            } else {
                lifestyleService.updateLifestyle(user.getId(), lifestyle.getId(), lifestyle);
            }
        }

        // FamilyDetails
        if (record.isSet("fathersName") || record.isSet("mothersName") || record.isSet("brother") || record.isSet("sister") ||
                record.isSet("fathersOccupation") || record.isSet("mothersOccupation") || record.isSet("familyLocation") || record.isSet("familyInfo")) {
            FamilyDetails family = familyDetailsService.getFamilyDetailsByUserId(user.getId()).stream().findFirst().orElse(new FamilyDetails());
            family.setProfileId(profile.getProfileId()); // Use auto-generated profileId
            family.setFathersName(record.get("fathersName"));
            family.setMothersName(record.get("mothersName"));
            family.setBrother(record.get("brother"));
            family.setSister(record.get("sister"));
            family.setFathersOccupation(record.get("fathersOccupation"));
            family.setMothersOccupation(record.get("mothersOccupation"));
            family.setFamilyLocation(record.get("familyLocation"));
            family.setFamilyInfo(record.get("familyInfo"));
            if (family.getId() == null) {
                familyDetailsService.createFamilyDetails(user.getId(), family);
            } else {
                familyDetailsService.updateFamilyDetails(user.getId(), family.getId(), family);
            }
        }

        // Contact
        if (record.isSet("phoneNumber") || record.isSet("secondaryMobileNo") || record.isSet("instaId") || record.isSet("linkedIn") || record.isSet("contactAdditionalInfo")) {
            Contact contact = contactService.getContactByProfileId(profile.getProfileId()).orElse(new Contact());
            contact.setProfileId(profile.getProfileId()); // Use auto-generated profileId
            contact.setPhoneNumber(record.get("phoneNumber"));
            contact.setSecondaryMobileNo(record.get("secondaryMobileNo"));
            contact.setInstaId(record.get("instaId"));
            contact.setLinkedIn(record.get("linkedIn"));
            contact.setAdditionalInfo(record.get("contactAdditionalInfo"));
            if (contact.getId() == null) {
                contactService.createContact(user.getId(), contact);
            } else {
                contactService.updateContact(user.getId(), contact.getId(), contact);
            }
        }

        // Carrier
        if (record.isSet("occupation") || record.isSet("organizationName") || record.isSet("department") || record.isSet("employedIn") ||
                record.isSet("designation") || record.isSet("earnings") || record.isSet("farmland")) {
            Carrier carrier = carrierService.getCarrierByUserId(user.getId()).orElse(new Carrier());
            carrier.setProfileId(profile.getProfileId()); // Use auto-generated profileId
            carrier.setOccupation(record.get("occupation"));
            carrier.setOrganizationName(record.get("organizationName"));
            carrier.setDepartment(record.get("department"));
            carrier.setEmployedIn(record.get("employedIn"));
            carrier.setDesignation(record.get("designation"));
            carrier.setEarnings(record.get("earnings"));
            carrier.setFarmland(record.get("farmland"));
            if (carrier.getId() == null) {
                carrierService.createCarrier(user.getId(), carrier);
            } else {
                carrierService.updateCarrier(user.getId(), carrier.getId(), carrier);
            }
        }
    }
}

class BulkUploadResponse {
    private int successCount;
    private List<String> errors;

    public BulkUploadResponse(int successCount, List<String> errors) {
        this.successCount = successCount;
        this.errors = errors;
    }

    public int getSuccessCount() { return successCount; }
    public List<String> getErrors() { return errors; }
}