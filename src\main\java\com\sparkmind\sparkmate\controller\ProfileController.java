package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.service.ProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/profiles")
public class ProfileController {

    private static final Logger logger = LoggerFactory.getLogger(ProfileController.class);

    @Autowired
    private ProfileService profileService;

    // Create a new profile for the authenticated user
    @PostMapping
    public ResponseEntity<Profile> createProfile() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.info("Creating profile for userId: {}", userId);
            Profile profile = profileService.createProfile(userId);
            return ResponseEntity.status(HttpStatus.CREATED).body(profile);
        } catch (Exception e) {
            logger.error("Error creating profile: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    // Get or create the authenticated user's profile
    @GetMapping("/me")
    public ResponseEntity<Profile> getMyProfile() {
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        logger.info("Fetching profile for userId: {}", userId);
        Profile profile = profileService.getProfileByUserId(userId).orElseGet(() -> {
            logger.warn("No profile found for userId: {}, creating one", userId);
            return profileService.createProfile(userId);
        });
        return ResponseEntity.ok(profile);
    }

    // Update profile by profileId
    @PutMapping("/{profileId}")
    public ResponseEntity<Profile> updateProfile(@PathVariable String profileId, @RequestBody Profile profile) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.info("Updating profileId: {} for userId: {}", profileId, userId);
            Profile existingProfile = profileService.getProfileByUserId(userId)
                    .filter(p -> p.getProfileId().equals(profileId))
                    .orElseThrow(() -> new RuntimeException("Profile not found or not owned by user"));
            Profile updatedProfile = profileService.updateProfile(profileId, profile);
            return ResponseEntity.ok(updatedProfile);
        } catch (RuntimeException e) {
            logger.error("Error updating profile: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }
}