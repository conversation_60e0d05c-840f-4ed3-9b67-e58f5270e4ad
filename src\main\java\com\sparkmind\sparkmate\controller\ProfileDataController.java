package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.dto.ErrorResponse;
import com.sparkmind.sparkmate.dto.ProfileDataDTO;
import com.sparkmind.sparkmate.model.*;
import com.sparkmind.sparkmate.service.*;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/profile-data")
@Validated
public class ProfileDataController {

    private static final Logger logger = LoggerFactory.getLogger(ProfileDataController.class);

    @Autowired
    private ProfileService profileService;

    @Autowired
    private FamilyDetailsService familyDetailsService;

    @Autowired
    private CarrierService carrierService;

    @Autowired
    private LifestyleService lifestyleService;

    @Autowired
    private ProfileInfoService profileInfoService;

    @Autowired
    private ProfileEducationDetailsService educationDetailsService;

    @Autowired
    private ProfileImagesService profileImagesService;

    @Autowired
    private ContactService contactService;

    @Autowired
    private FavouriteTracksService favouriteTracksService;

    @PostMapping(consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<?> saveAllProfileData(
            @Valid @RequestPart("profileData") ProfileDataDTO profileDataDTO,
            @RequestPart(value = "images", required = false) MultipartFile[] images) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            Profile authenticatedProfile = profileService.getProfileByUserId(userId)
                    .orElseThrow(() -> new IllegalArgumentException("Profile not found for authenticated user"));

            if (!authenticatedProfile.getProfileId().equals(profileDataDTO.getProfile().getProfileId())) {
                logger.warn("User {} attempted to update profile {} they don’t own", userId, profileDataDTO.getProfile().getProfileId());
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ErrorResponse(HttpStatus.FORBIDDEN, "Forbidden", "You can only update your own profile"));
            }

            logger.info("Saving all profile data for userId: {}", userId);
            ProfileDataDTO savedData = saveProfileComponents(userId, profileDataDTO, images);
            return ResponseEntity.status(HttpStatus.CREATED).body(savedData);
        } catch (IllegalArgumentException e) {
            logger.error("Error saving profile data: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse(HttpStatus.BAD_REQUEST, "Bad Request", e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error saving profile data: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "Server Error", "An unexpected error occurred"));
        }
    }

    @GetMapping("/me")
    public ResponseEntity<ProfileDataDTO> getMyProfileData() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            Profile profile = profileService.getProfileByUserId(userId)
                    .orElseThrow(() -> new IllegalArgumentException("Profile not found for authenticated user"));

            logger.info("Fetching profile data for userId: {}", userId);
            ProfileDataDTO profileData = fetchProfileData(profile.getProfileId());
            return ResponseEntity.ok(profileData);
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching profile data: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error fetching profile data: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/{uid}")
    public ResponseEntity<ProfileDataDTO> getProfileDataByUid(@PathVariable String uid) {
        try {
            Profile profile = profileService.getProfileByUserId(uid)
                    .orElseThrow(() -> new IllegalArgumentException("Profile not found for UID: " + uid));

            logger.info("Fetching profile data for uid: {}", uid);
            ProfileDataDTO profileData = fetchProfileData(profile.getProfileId());
            return ResponseEntity.ok(profileData);
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching profile data for uid {}: {}", uid, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error fetching profile data for uid {}: {}", uid, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @PutMapping("/primary-image")
    public ResponseEntity<?> setPrimaryImage(@RequestParam("index") int imageIndex) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.info("Setting primary image for userId: {} at index: {}", userId, imageIndex);

            ProfileImages updatedImages = profileImagesService.setPrimaryProfileImage(userId, imageIndex);
            ProfileDataDTO updatedData = fetchProfileData(updatedImages.getProfileId());
            return ResponseEntity.ok(updatedData);
        } catch (IllegalArgumentException e) {
            logger.error("Error setting primary image: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse(HttpStatus.BAD_REQUEST, "Bad Request", e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error setting primary image: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "Server Error", "Failed to set primary image"));
        }
    }

    private ProfileDataDTO saveProfileComponents(String userId, ProfileDataDTO profileDataDTO, MultipartFile[] images) throws IOException {
        Profile profile = profileService.updateProfile(profileDataDTO.getProfile().getProfileId(), profileDataDTO.getProfile());
        String profileId = profile.getProfileId();

        ProfileDataDTO savedData = new ProfileDataDTO();
        savedData.setProfile(profile);

        if (profileDataDTO.getFamilyDetails() != null) {
            savedData.setFamilyDetails(familyDetailsService.createFamilyDetails(userId, profileDataDTO.getFamilyDetails()));
        }

        if (profileDataDTO.getCarrierDetails() != null) {
            savedData.setCarrierDetails(carrierService.createCarrier(userId, profileDataDTO.getCarrierDetails()));
        }

        if (profileDataDTO.getLifestyleDetails() != null) {
            savedData.setLifestyleDetails(lifestyleService.createLifestyle(userId, profileDataDTO.getLifestyleDetails()));
        }

        if (profileDataDTO.getProfileInfo() != null) {
            savedData.setProfileInfo(profileInfoService.createProfileInfo(userId, profileDataDTO.getProfileInfo()));
        }

        if (profileDataDTO.getEducationDetails() != null) {
            savedData.setEducationDetails(educationDetailsService.addEducation(userId, profileDataDTO.getEducationDetails()));
        }

        if (images != null && images.length > 0) {
            ProfileImages savedImages = profileImagesService.uploadOrUpdateProfileImages(userId, images);
            savedData.setProfileImage(savedImages);
        } else {
            ProfileImages existingImages = profileImagesService.retrieveProfileImages(profileId);
            savedData.setProfileImage(existingImages);
        }

        if (profileDataDTO.getContact() != null) {
            savedData.setContact(contactService.createContact(userId, profileDataDTO.getContact()));
        }

        return savedData;
    }

    private ProfileDataDTO fetchProfileData(String profileId) {
        ProfileDataDTO profileData = new ProfileDataDTO();
        Profile profile = profileService.getProfileByProfileId(profileId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));

        profileData.setProfile(profile);

        profileData.setFamilyDetails(familyDetailsService.getFamilyDetailsByUserId(profile.getUserId())
                .stream().findFirst().orElse(null));
        if (profileData.getFamilyDetails() == null) {
            logger.info("No family details found for profileId: {}", profileId);
        }

        profileData.setCarrierDetails(carrierService.getCarrierByUserId(profile.getUserId()).orElse(null));
        if (profileData.getCarrierDetails() == null) {
            logger.info("No carrier details found for profileId: {}", profileId);
        }

        List<Lifestyle> lifestyles = lifestyleService.getLifestyleByUserId(profile.getUserId());
        profileData.setLifestyleDetails(lifestyles.isEmpty() ? null : lifestyles.get(0));
        if (profileData.getLifestyleDetails() == null) {
            logger.info("No lifestyle details found for profileId: {}", profileId);
        }

        profileData.setProfileInfo(profileInfoService.getProfileInfoByUserId(profile.getUserId()).orElse(null));
        if (profileData.getProfileInfo() == null) {
            logger.info("No profile info found for profileId: {}", profileId);
        }

        profileData.setEducationDetails(educationDetailsService.getEducation(profile.getUserId()).orElse(null));
        if (profileData.getEducationDetails() == null) {
            logger.info("No education details found for profileId: {}", profileId);
        }

        profileData.setProfileImage(profileImagesService.retrieveProfileImages(profileId));
        if (profileData.getProfileImage() == null) {
            logger.info("No profile images found for profileId: {}", profileId);
        } else {
            logger.debug("Profile images retrieved for profileId: {}, primaryImage present: {}",
                    profileId, profileData.getProfileImage().getPrimaryImage() != null);
        }

        profileData.setContact(contactService.getContactByProfileId(profileId).orElse(null));
        if (profileData.getContact() == null) {
            logger.info("No contact details found for profileId: {}", profileId);
        }

        // Add favorite tracking
        String authenticatedUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        Optional<FavouriteTracks> favouriteOpt = favouriteTracksService.findBySenderUserIdAndReceiverProfileId(authenticatedUserId, profileId);
        profileData.setFavouriteTracks(favouriteOpt.orElse(null));
        profileData.setIsFavorited(favouriteOpt.isPresent());
        logger.debug("Favorite status for profileId {} by userId {}: {}", profileId, authenticatedUserId, profileData.getIsFavorited());

        return profileData;
    }
}