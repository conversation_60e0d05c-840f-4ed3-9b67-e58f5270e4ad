package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.ProfileEducationDetails;
import com.sparkmind.sparkmate.service.ProfileEducationDetailsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/education")
public class ProfileEducationDetailsController {

    private static final Logger logger = LoggerFactory.getLogger(ProfileEducationDetailsController.class);

    @Autowired
    private ProfileEducationDetailsService service;

    @PostMapping
    public ResponseEntity<ProfileEducationDetails> addEducation(@Valid @RequestBody ProfileEducationDetails educationDetails) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Adding education details for userId: {}", userId);
            ProfileEducationDetails createdEducation = service.addEducation(userId, educationDetails);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdEducation);
        } catch (IllegalArgumentException e) {
            logger.error("Error adding education: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error adding education: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/me")
    public ResponseEntity<ProfileEducationDetails> getMyEducation() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Fetching education details for userId: {}", userId);
            Optional<ProfileEducationDetails> education = service.getEducation(userId);
            return education.map(ed -> ResponseEntity.ok()
                            .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                            .body(ed))
                    .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND).build());
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching education: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (Exception e) {
            logger.error("Unexpected error fetching education: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/all")
    public ResponseEntity<List<ProfileEducationDetails>> getAllEducationDetails() {
        try {
            logger.debug("Fetching all education details");
            List<ProfileEducationDetails> educationDetails = service.getAllEducationDetails();
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(10, TimeUnit.MINUTES))
                    .body(educationDetails);
        } catch (Exception e) {
            logger.error("Unexpected error fetching all education details: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/{educationId}")
    public ResponseEntity<ProfileEducationDetails> updateEducation(
            @PathVariable String educationId,
            @Valid @RequestBody ProfileEducationDetails updatedEducationDetails) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Updating education details id: {} for userId: {}", educationId, userId);
            ProfileEducationDetails updatedEducation = service.updateEducation(userId, educationId, updatedEducationDetails);
            return ResponseEntity.ok(updatedEducation);
        } catch (IllegalArgumentException e) {
            logger.error("Error updating education: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error updating education: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @DeleteMapping("/{profileId}")
    public ResponseEntity<Void> deleteEducation(@PathVariable String profileId) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Deleting education details for profileId: {} by userId: {}", profileId, userId);
            service.deleteEducation(userId, profileId);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            logger.error("Error deleting education: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (Exception e) {
            logger.error("Unexpected error deleting education: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}