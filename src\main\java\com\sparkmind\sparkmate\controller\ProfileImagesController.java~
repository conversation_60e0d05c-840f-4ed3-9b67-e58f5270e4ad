package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.ProfileImages;
import com.sparkmind.sparkmate.service.ProfileImagesService;
import com.sparkmind.sparkmate.service.ProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/profile-images")
public class ProfileImagesController {

    private static final Logger logger = LoggerFactory.getLogger(ProfileImagesController.class);

    private final ProfileImagesService service;
    private final ProfileService profileService;

    public ProfileImagesController(ProfileImagesService service, ProfileService profileService) {
        this.service = service;
        this.profileService = profileService;
    }

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> uploadImages(@RequestParam("images") MultipartFile[] files) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Uploading {} images for userId: {}", files.length, userId);
            ProfileImages savedImages = service.uploadOrUpdateProfileImages(userId, files);
            return ResponseEntity.status(HttpStatus.CREATED).body(savedImages);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid request uploading images: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (IOException e) {
            logger.error("IO error uploading images: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to upload images to storage: " + e.getMessage());
        }
    }

    @GetMapping("/me")
    public ResponseEntity<?> getMyImages() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Fetching images for userId: {}", userId);
            String profileId = profileService.getProfileByUserId(userId)
                    .orElseThrow(() -> new IllegalArgumentException("Profile not found for user: " + userId))
                    .getProfileId();
            ProfileImages images = service.retrieveProfileImages(profileId);
            if (images == null) {
                logger.debug("No images found for userId: {}, returning empty object", userId);
                return ResponseEntity.ok()
                        .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                        .body(new ProfileImages());
            }
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                    .body(images);
        } catch (IllegalArgumentException e) {
            logger.error("Error retrieving images: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        }
    }

    @GetMapping("/{profileId}")
    public ResponseEntity<?> getImages(@PathVariable String profileId) {
        try {
            logger.debug("Fetching images for profileId: {}", profileId);
            ProfileImages images = service.retrieveProfileImages(profileId);
            if (images == null || (images.getImages().isEmpty() && images.getPrimaryImage() == null)) {
                logger.debug("No images found for profileId: {}", profileId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("No images found for profile: " + profileId);
            }
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                    .body(images);
        } catch (IllegalArgumentException e) {
            logger.error("Error retrieving images: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        }
    }

    @DeleteMapping
    public ResponseEntity<?> removeImages() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Removing images for userId: {}", userId);
            service.removeProfileImages(userId);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            logger.error("Error removing images: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        }
    }

    @PutMapping("/primary")
    public ResponseEntity<?> setPrimaryImage(@RequestParam("index") int imageIndex) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Setting primary image for userId: {} at index: {}", userId, imageIndex);
            ProfileImages updatedImages = service.setPrimaryProfileImage(userId, imageIndex);
            return ResponseEntity.ok(updatedImages);
        } catch (IllegalArgumentException e) {
            logger.error("Error setting primary image: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/public")
    public ResponseEntity<?> makeImagePublic(@RequestParam("index") int imageIndex) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Making image public for userId: {} at index: {}", userId, imageIndex);
            ProfileImages updatedImages = service.makeImagePublic(userId, imageIndex);
            return ResponseEntity.ok(updatedImages);
        } catch (IllegalArgumentException e) {
            logger.error("Error making image public: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }
}