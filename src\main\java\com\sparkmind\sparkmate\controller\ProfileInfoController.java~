package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.ProfileInfo;
import com.sparkmind.sparkmate.model.User;
import com.sparkmind.sparkmate.service.ProfileInfoService;
import com.sparkmind.sparkmate.service.ProfileService;
import com.sparkmind.sparkmate.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/profile-info")
public class ProfileInfoController {

    private static final Logger logger = LoggerFactory.getLogger(ProfileInfoController.class);

    @Autowired
    private ProfileInfoService profileInfoService;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private UserService userService;

    @PostMapping
    public ResponseEntity<ProfileInfo> createProfileInfo(@Valid @RequestBody ProfileInfo profileInfo) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Creating profile info for userId: {}", userId);
            Profile profile = profileService.getProfileByUserId(userId)
                    .orElseGet(() -> profileService.createProfile(userId));
            ProfileInfo createdProfileInfo = profileInfoService.createProfileInfo(profile.getProfileId(), profileInfo);

            if (profileInfo.getFirstName() != null && !profileInfo.getFirstName().isEmpty()) {
                User user = userService.getUserById(userId)
                        .orElseThrow(() -> new RuntimeException("User not found after sync"));
                user.setName(profileInfo.getFirstName());
                userService.updateUser(userId, user);
                logger.info("Set User.name to ProfileInfo.firstName: {} for userId: {}", profileInfo.getFirstName(), userId);
            }

            return ResponseEntity.status(HttpStatus.CREATED).body(createdProfileInfo);
        } catch (RuntimeException e) {
            logger.error("Error creating profile info: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<ProfileInfo> updateProfileInfo(@PathVariable String id, @Valid @RequestBody ProfileInfo profileInfo) {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Updating profile info id: {} for userId: {}", id, userId);
            Profile profile = profileService.getProfileByUserId(userId)
                    .orElseThrow(() -> {
                        logger.warn("Profile not found for userId: {}", userId);
                        return new RuntimeException("Profile not found for user");
                    });
            ProfileInfo existingProfileInfo = profileInfoService.getProfileInfoByProfileId(profile.getProfileId())
                    .filter(pi -> pi.getId().equals(id))
                    .orElseThrow(() -> {
                        logger.warn("ProfileInfo not found or not owned by userId: {}", userId);
                        return new RuntimeException("ProfileInfo not found or not owned by user");
                    });
            ProfileInfo updatedProfileInfo = profileInfoService.updateProfileInfo(id, profileInfo);

            if (profileInfo.getFirstName() != null && !profileInfo.getFirstName().isEmpty()) {
                User user = userService.getUserById(userId)
                        .orElseThrow(() -> new RuntimeException("User not found after sync"));
                user.setName(profileInfo.getFirstName());
                userService.updateUser(userId, user);
                logger.info("Set User.name to ProfileInfo.firstName: {} for userId: {}", profileInfo.getFirstName(), userId);
            }

            return ResponseEntity.ok(updatedProfileInfo);
        } catch (RuntimeException e) {
            logger.error("Error updating profile info: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    @GetMapping("/me")
    public ResponseEntity<ProfileInfo> getMyProfileInfo() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            logger.debug("Fetching profile info for userId: {}", userId);
            Optional<Profile> profileOpt = profileService.getProfileByUserId(userId);
            if (profileOpt.isEmpty()) {
                logger.warn("Profile not found for userId: {}", userId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
            }
            Profile profile = profileOpt.get();
            logger.info("Fetching profile with profileId: {}", profile.getProfileId()); // Matches your error log
            Optional<ProfileInfo> profileInfoOpt = profileInfoService.getProfileInfoByProfileId(profile.getProfileId());
            if (profileInfoOpt.isEmpty()) {
                logger.warn("ProfileInfo not found for profileId: {}", profile.getProfileId());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
            }
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
                    .body(profileInfoOpt.get());
        } catch (RuntimeException e) {
            logger.error("Error fetching profile info: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}