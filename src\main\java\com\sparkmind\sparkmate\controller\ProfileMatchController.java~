package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.dto.ProfileMatchDTO;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.service.ProfileMatchService;
import com.sparkmind.sparkmate.service.ProfileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/profile-matches")
public class ProfileMatchController {

    private static final Logger logger = LoggerFactory.getLogger(ProfileMatchController.class);

    @Autowired
    private ProfileMatchService profileMatchService;

    @Autowired
    private ProfileService profileService;

    @GetMapping("/me")
    public ResponseEntity<List<ProfileMatchDTO>> getMyMatchingProfiles() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            Profile profile = profileService.getProfileByUserId(userId)
                    .orElseThrow(() -> new IllegalArgumentException("Profile not found for user"));
            logger.info("Fetching matching profiles for profileId: {}", profile.getProfileId());
            List<ProfileMatchDTO> matches = profileMatchService.getMatchingProfiles(profile.getProfileId());
            return ResponseEntity.ok(matches);
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching matches: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error fetching matches: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
}