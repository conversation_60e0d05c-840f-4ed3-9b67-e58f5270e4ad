package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.dto.ProfileVisitCardDTO;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.ProfileVisits;
import com.sparkmind.sparkmate.service.ProfileService;
import com.sparkmind.sparkmate.service.ProfileVisitsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/profile-visits")
public class ProfileVisitsController {

    private static final Logger logger = LoggerFactory.getLogger(ProfileVisitsController.class);

    @Autowired
    private ProfileVisitsService profileVisitsService;

    @Autowired
    private ProfileService profileService;

    @PostMapping
    public ResponseEntity<ProfileVisits> recordProfileVisit(@RequestBody ProfileVisits profileVisit) {
        try {
            String authenticatedUserId = SecurityContextHolder.getContext().getAuthentication().getName();
            if (!authenticatedUserId.equals(profileVisit.getVisitFromUserId())) {
                logger.warn("Forbidden: User {} tried to record visit as {}", authenticatedUserId, profileVisit.getVisitFromUserId());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
            if (profileVisit.getVisitToProfile() == null || profileVisit.getVisitFromUserId() == null) {
                logger.error("Bad Request: Missing visitToProfile or visitFromUserId");
                return ResponseEntity.badRequest().body(null);
            }

            logger.info("Recording visit from userId {} to profileId {}", profileVisit.getVisitFromUserId(), profileVisit.getVisitToProfile());
            ProfileVisits recordedVisit = profileVisitsService.recordProfileVisit(
                    profileVisit.getVisitToProfile(),
                    profileVisit.getVisitFromUserId()
            );
            return ResponseEntity.status(HttpStatus.CREATED).body(recordedVisit);
        } catch (IllegalArgumentException e) {
            logger.error("Error recording visit: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error recording visit: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/me")
    public ResponseEntity<List<ProfileVisitCardDTO>> getMyProfileVisits() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            Profile profile = profileService.getProfileByUserId(userId)
                    .orElseThrow(() -> new IllegalArgumentException("Profile not found for user: " + userId));
            logger.info("Fetching all visits for profileId: {}", profile.getProfileId());
            List<ProfileVisitCardDTO> visits = profileVisitsService.getAllProfileVisits(profile.getProfileId());
            return ResponseEntity.ok(visits != null ? visits : List.of());
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching visits: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error fetching visits: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/me/recent")
    public ResponseEntity<ProfileVisitCardDTO> getMyMostRecentVisit() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            Profile profile = profileService.getProfileByUserId(userId)
                    .orElseThrow(() -> new IllegalArgumentException("Profile not found for user: " + userId));
            logger.info("Fetching most recent visit for profileId: {}", profile.getProfileId());
            ProfileVisitCardDTO visit = profileVisitsService.getMostRecentVisit(profile.getProfileId());
            return ResponseEntity.ok(visit);
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching recent visit: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error fetching recent visit: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/my-visits")
    public ResponseEntity<List<ProfileVisitCardDTO>> getMyVisitedProfiles() {
        try {
            String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            List<Profile> activeProfiles = profileService.getAllActiveProfiles();
            List<String> activeProfileIds = activeProfiles.stream().map(Profile::getProfileId).collect(Collectors.toList());
            logger.info("Fetching all profiles visited by userId: {}", userId);
            List<ProfileVisitCardDTO> visits = profileVisitsService.getProfilesVisitedByUser(userId, activeProfileIds);
            return ResponseEntity.ok(visits);
        } catch (IllegalArgumentException e) {
            logger.error("Error fetching my visited profiles: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            logger.error("Unexpected error fetching my visited profiles: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
}