package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.SubscriptionPlan;
import com.sparkmind.sparkmate.model.Subscriptions;
import com.sparkmind.sparkmate.service.SubscriptionPlanService;
import com.sparkmind.sparkmate.service.SubscriptionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/subscriptions")
public class SubscriptionController {
    private static final Logger logger = LoggerFactory.getLogger(SubscriptionController.class);

    private final SubscriptionService subscriptionService;
    @Autowired
    private SubscriptionPlanService subscriptionPlanService;

    public SubscriptionController(SubscriptionService subscriptionService) {
        this.subscriptionService = subscriptionService;
    }

    @PostMapping("/initiate")
    public ResponseEntity<?> initiateSubscription(@RequestParam String userId, @RequestParam String planId) {
        try {
            logger.info("Initiating subscription for user {} with plan {}", userId, planId);
            Map<String, Object> orderDetails = subscriptionService.initiateSubscription(userId, planId);
            return ResponseEntity.ok(orderDetails);
        } catch (RuntimeException e) {
            logger.error("Error initiating subscription: {}", e.getMessage());
            return ResponseEntity.status(404).body("Error: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error initiating subscription: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Unexpected error: " + e.getMessage());
        }
    }

    @PostMapping("/update-payment")
    public ResponseEntity<?> updatePayment(
            @RequestParam String subscriptionId,
            @RequestParam String transactionId,
            @RequestParam String paymentStatus) {
        try {
            logger.info("Updating payment for subscription {} with status {}", subscriptionId, paymentStatus);
            Subscriptions sub = subscriptionService.updateSubscriptionPayment(subscriptionId, transactionId, paymentStatus);
            return ResponseEntity.ok(sub);
        } catch (RuntimeException e) {
            logger.error("Error updating payment: {}", e.getMessage());
            return ResponseEntity.status(404).body("Error: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error updating payment: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Unexpected error: " + e.getMessage());
        }
    }

    @GetMapping("/current")
    public ResponseEntity<?> getCurrentSubscription(@RequestParam String userId) {
        try {
            logger.info("Fetching current subscription for user {}", userId);
            Subscriptions sub = subscriptionService.getCurrentSubscription(userId);
            return ResponseEntity.ok(sub != null ? sub : new Subscriptions());
        } catch (Exception e) {
            logger.error("Error fetching current subscription: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Error fetching subscription: " + e.getMessage());
        }
    }

    @GetMapping("/plans")
    public ResponseEntity<List<SubscriptionPlan>> getAvailablePlans() {
        logger.info("Fetching available subscription plans");
        List<SubscriptionPlan> plans = subscriptionPlanService.getAllPlans();
        return ResponseEntity.ok(plans);
    }
}