package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.SubscriptionPlan;
import com.sparkmind.sparkmate.service.SubscriptionPlanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/plans")
public class SubscriptionPlanController {
    private static final Logger logger = LoggerFactory.getLogger(SubscriptionPlanController.class);

    private final SubscriptionPlanService subscriptionPlanService;

    public SubscriptionPlanController(SubscriptionPlanService subscriptionPlanService) {
        this.subscriptionPlanService = subscriptionPlanService;
    }

    @PostMapping("/create")
    public ResponseEntity<?> createPlan(@Valid @RequestBody SubscriptionPlan plan) {
        try {
            logger.info("Creating plan: {}", plan.getPlanName());
            SubscriptionPlan createdPlan = subscriptionPlanService.createPlan(plan);
            return ResponseEntity.ok(createdPlan);
        } catch (RuntimeException e) {
            logger.error("Error creating plan: {}", e.getMessage());
            return ResponseEntity.status(409).body("Error: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error creating plan: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Unexpected error: " + e.getMessage());
        }
    }

    @GetMapping("/{planId}")
    public ResponseEntity<?> getPlan(@PathVariable String planId) {
        try {
            logger.info("Fetching plan: {}", planId);
            SubscriptionPlan plan = subscriptionPlanService.getPlanById(planId);
            return ResponseEntity.ok(plan);
        } catch (RuntimeException e) {
            logger.error("Plan not found: {}", e.getMessage());
            return ResponseEntity.status(404).body("Plan not found: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error fetching plan: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Unexpected error: " + e.getMessage());
        }
    }

    @GetMapping
    public ResponseEntity<?> getAllPlans() {
        try {
            logger.info("Fetching all plans");
            List<SubscriptionPlan> plans = subscriptionPlanService.getAllPlans();
            return ResponseEntity.ok(plans);
        } catch (Exception e) {
            logger.error("Error fetching all plans: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Error fetching plans: " + e.getMessage());
        }
    }

    @PutMapping("/{planId}")
    public ResponseEntity<?> updatePlan(@PathVariable String planId, @Valid @RequestBody SubscriptionPlan plan) {
        try {
            logger.info("Updating plan: {}", planId);
            SubscriptionPlan updatedPlan = subscriptionPlanService.updatePlan(planId, plan);
            return ResponseEntity.ok(updatedPlan);
        } catch (RuntimeException e) {
            logger.error("Error updating plan: {}", e.getMessage());
            return ResponseEntity.status(404).body("Error: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error updating plan: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Unexpected error: " + e.getMessage());
        }
    }

    @DeleteMapping("/{planId}")
    public ResponseEntity<?> deletePlan(@PathVariable String planId) {
        try {
            logger.info("Deleting plan: {}", planId);
            subscriptionPlanService.deletePlan(planId);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            logger.error("Error deleting plan: {}", e.getMessage());
            return ResponseEntity.status(404).body("Plan not found: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error deleting plan: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Unexpected error: " + e.getMessage());
        }
    }
}