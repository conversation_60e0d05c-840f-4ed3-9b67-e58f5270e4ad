package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.dto.ErrorResponse;
import com.sparkmind.sparkmate.model.SubscriptionConfig;
import com.sparkmind.sparkmate.model.Subscriptions;
import com.sparkmind.sparkmate.repository.SubscriptionConfigRepository;
import com.sparkmind.sparkmate.service.SubscriptionManagerService;
import com.sparkmind.sparkmate.service.SubscriptionService;
import com.sparkmind.sparkmate.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

@RestController
@RequestMapping("/api/user-activity")
public class UserActivityController {

    private static final Logger logger = LoggerFactory.getLogger(UserActivityController.class);

    @Autowired
    private SubscriptionManagerService subscriptionManagerService;

    @Autowired
    private SubscriptionConfigRepository subscriptionConfigRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private SubscriptionService subscriptionService;

    @GetMapping("/admin/config")
    public ResponseEntity<Iterable<SubscriptionConfig>> getAllConfigs() {
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        if (!userService.isAdmin(userId)) {
            logger.warn("Unauthorized access attempt by user {} to admin config", userId);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        return ResponseEntity.ok(subscriptionConfigRepository.findAll());
    }

    @PostMapping("/admin/config")
    public ResponseEntity<SubscriptionConfig> updateConfig(@RequestBody SubscriptionConfig config) {
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        if (!userService.isAdmin(userId)) {
            logger.warn("Unauthorized access attempt by user {} to update config", userId);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        config.setUpdatedAt(LocalDateTime.now());
        SubscriptionConfig saved = subscriptionConfigRepository.save(config);
        logger.info("Updated subscription config for plan {}", config.getPlanId());
        return ResponseEntity.ok(saved);
    }

    @PostMapping("/visit/{profileId}")
    public ResponseEntity<Void> trackProfileVisit(@PathVariable String profileId) {
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        boolean allowed = subscriptionManagerService.trackProfileVisit(userId, profileId);
        if (!allowed) {
            logger.warn("Profile visit restricted for user {}", userId);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).header("X-Redirect", "/subscribe").build();
        }
        return ResponseEntity.ok().build();
    }

    @PostMapping("/message")
    public ResponseEntity<Void> trackMessage(@RequestBody Map<String, String> payload) {
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        String recipientUserId = payload.get("recipientUserId");
        boolean allowed = subscriptionManagerService.incrementMessageCount(userId, recipientUserId);
        if (!allowed) {
            logger.warn("Messaging restricted for user {}", userId);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).header("X-Redirect", "/subscribe").build();
        }
        return ResponseEntity.ok().build();
    }

    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getVisitStats() {
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        return ResponseEntity.ok(subscriptionManagerService.getVisitStats(userId));
    }

    @GetMapping("/limits")
    public ResponseEntity<Map<String, Integer>> getActivityLimits() {
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        return ResponseEntity.ok(subscriptionManagerService.getActivityLimits(userId));
    }

    @GetMapping("/badge")
    public ResponseEntity<String> getUserBadge() {
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        return ResponseEntity.ok(subscriptionManagerService.getUserBadge(userId));
    }

    @GetMapping("/subscriptions/config")
    public ResponseEntity<Iterable<SubscriptionConfig>> getAllConfigsPublic() {
        return ResponseEntity.ok(subscriptionConfigRepository.findAll());
    }

    @PostMapping("/subscriptions/activate-basic")
    public ResponseEntity<?> activateBasicPlan(@RequestBody Map<String, String> payload) {
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        String planId = payload.get("planId");
        try {
            Subscriptions subscription = subscriptionService.activateBasicPlan(userId, planId);
            logger.info("Basic plan activated for user {}", userId);
            return ResponseEntity.ok(subscription);
        } catch (IllegalArgumentException | IllegalStateException e) {
            logger.error("Error activating Basic plan for user {}: {}", userId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse(HttpStatus.BAD_REQUEST, "Bad Request", e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error activating Basic plan for user {}: {}", userId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "Server Error", "An unexpected error occurred"));
        }
    }
}