package com.sparkmind.sparkmate.controller;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.UserRecord;
import com.sparkmind.sparkmate.model.User;
import com.sparkmind.sparkmate.service.ProfileService;
import com.sparkmind.sparkmate.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/users")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private ProfileService profileService;

    @PostMapping
    public ResponseEntity<User> createUser(@RequestBody User user) {
        try {
            User createdUser = userService.createUser(user);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body(null);
        }
    }

    @GetMapping
    public ResponseEntity<List<User>> getAllUsers() {
        List<User> users = userService.getAllUsers();
        return ResponseEntity.ok(users);
    }

    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable String id) {
        try {
            return userService.getUserById(id)
                    .map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            System.err.println("Error fetching user " + id + ": " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable String id, @RequestBody User user) {
        try {
            User updatedUser = userService.updateUser(id, user);
            return ResponseEntity.ok(updatedUser);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable String id) {
        try {
            userService.deleteUser(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @PostMapping("/sync")
    public ResponseEntity<User> syncUser(@RequestBody User syncUserData) {
        try {
            String uid = SecurityContextHolder.getContext().getAuthentication().getName();
            User userFromAuth = new User();
            userFromAuth.setId(uid);
            UserRecord firebaseUser = FirebaseAuth.getInstance().getUser(uid);
            userFromAuth.setEmail(firebaseUser.getEmail());
            userFromAuth.setName(firebaseUser.getDisplayName());
            userFromAuth.setMobile(syncUserData.getMobile() != null ? syncUserData.getMobile() : firebaseUser.getPhoneNumber());
            userFromAuth.setRole(syncUserData.getRole() != null ? syncUserData.getRole() : "Customer");
            userFromAuth.setLookingFor(syncUserData.getLookingFor());
            userFromAuth.setStatus("Active");
            userFromAuth.setMarriageBureauId(null); // Initialize as null
            userService.syncUserFromFirestore(userFromAuth);
            return userService.getUserById(uid)
                    .map(ResponseEntity::ok)
                    .orElse(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build());
        } catch (Exception e) {
            System.err.println("Error syncing user: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}