package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.dto.FavouriteProfileDTO;
import com.sparkmind.sparkmate.dto.ProfileCardDTO;
import com.sparkmind.sparkmate.model.Lifestyle;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.User;
import com.sparkmind.sparkmate.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/user-profiles")
public class UserProfileController {

    private static final Logger logger = LoggerFactory.getLogger(UserProfileController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private ProfileInfoService profileInfoService;

    @Autowired
    private ProfileImagesService profileImagesService;

    @Autowired
    private CarrierService carrierService;

    @Autowired
    private LifestyleService lifestyleService;

    @Autowired
    private FavouriteTracksService favouriteTracksService;

    @Autowired
    private InterestTrackService interestTrackService;

    @Autowired
    private SubscriptionManagerService subscriptionManagerService;


    @GetMapping("/public/recent")
    public ResponseEntity<List<ProfileCardDTO>> getRecentPublicProfiles() {
        logger.info("Fetching recent public profiles for landing page (5 brides, 5 grooms)");

        // Fetch 5 recent brides
        Pageable pageableBrides = PageRequest.of(0, 10);
        List<User> brideUsers = userService.findByStatus("Active", pageableBrides);
        Map<String, Profile> brideProfileMap = profileService.getProfilesByUserIds(
                brideUsers.stream().map(User::getId).collect(Collectors.toList())
        );

        // Fetch 5 recent grooms
        Pageable pageableGrooms = PageRequest.of(0, 10);
        List<User> groomUsers = userService.findByStatus("Active", pageableGrooms);
        Map<String, Profile> groomProfileMap = profileService.getProfilesByUserIds(
                groomUsers.stream().map(User::getId).collect(Collectors.toList())
        );

        // Combine profile IDs for lifestyle fetch
        List<String> profileIds = new ArrayList<>();
        profileIds.addAll(brideProfileMap.values().stream().map(Profile::getProfileId).collect(Collectors.toList()));
        profileIds.addAll(groomProfileMap.values().stream().map(Profile::getProfileId).collect(Collectors.toList()));
        List<Lifestyle> lifestyles = lifestyleService.getLifestyleByProfileIds(profileIds);

        // Process brides
        List<ProfileCardDTO> brideProfiles = brideUsers.parallelStream()
                .filter(user -> brideProfileMap.containsKey(user.getId()))
                .map(user -> populateProfileCardDTOAsync(user, brideProfileMap.get(user.getId())))
                .map(CompletableFuture::join)
                .filter(dto -> dto != null && "Female".equalsIgnoreCase(dto.getGender()))
                .peek(dto -> {
                    lifestyles.stream()
                            .filter(l -> l.getProfileId().equals(dto.getProfileId()))
                            .findFirst()
                            .ifPresent(lifestyle -> {
                                List<String> preferences = lifestyle.getPreferences() != null ?
                                        lifestyle.getPreferences().stream()
                                                .map(Object::toString)
                                                .limit(3)
                                                .collect(Collectors.toList()) :
                                        Collections.emptyList();
                                dto.setLifestylePreferences(preferences);
                            });
                    dto.setBadge(subscriptionManagerService.getUserBadge(dto.getUserId()));
                })
                .collect(Collectors.toList());

        // Process grooms
        List<ProfileCardDTO> groomProfiles = groomUsers.parallelStream()
                .filter(user -> groomProfileMap.containsKey(user.getId()))
                .map(user -> populateProfileCardDTOAsync(user, groomProfileMap.get(user.getId())))
                .map(CompletableFuture::join)
                .filter(dto -> dto != null && "Male".equalsIgnoreCase(dto.getGender()))
                .peek(dto -> {
                    lifestyles.stream()
                            .filter(l -> l.getProfileId().equals(dto.getProfileId()))
                            .findFirst()
                            .ifPresent(lifestyle -> {
                                List<String> preferences = lifestyle.getPreferences() != null ?
                                        lifestyle.getPreferences().stream()
                                                .map(Object::toString)
                                                .limit(3)
                                                .collect(Collectors.toList()) :
                                        Collections.emptyList();
                                dto.setLifestylePreferences(preferences);
                            });
                    dto.setBadge(subscriptionManagerService.getUserBadge(dto.getUserId()));
                })
                .collect(Collectors.toList());

        // Combine and limit to 10 profiles
        List<ProfileCardDTO> combinedProfiles = new ArrayList<>();
        combinedProfiles.addAll(brideProfiles);
        combinedProfiles.addAll(groomProfiles);
        combinedProfiles = combinedProfiles.stream()
                .limit(10)
                .map(dto -> {
                    ProfileCardDTO newDto = new ProfileCardDTO();
                    BeanUtils.copyProperties(dto, newDto);
                    newDto.setFavorited(false); // No user context, so not favorited
                    newDto.setInterested(false); // No user context, so not interested
                    newDto.setBadge(dto.getBadge()); // Preserve badge
                    return newDto;
                })
                .collect(Collectors.toList());

        logger.info("Returning {} public profiles", combinedProfiles.size());
        if (combinedProfiles.isEmpty()) {
            logger.warn("No public profiles found. Check ProfileInfo.gender and Profile.lastSeen data.");
        }
        return ResponseEntity.ok(combinedProfiles);
    }


    @GetMapping("/all")
    public ResponseEntity<List<ProfileCardDTO>> getAllUserProfiles() {
        String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();


        logger.info("Fetching all user profiles");
        List<User> users = userService.getAllUsers();
        Map<String, Profile> profileMap = profileService.getProfilesByUserIds(
                users.stream().map(User::getId).collect(Collectors.toList())
        );

        List<ProfileCardDTO> baseProfiles = users.parallelStream()
                .filter(user -> profileMap.containsKey(user.getId()))
                .map(user -> populateProfileCardDTOAsync(user, profileMap.get(user.getId())))
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        Set<String> favoriteProfileIds = favouriteTracksService.getAllFavourites(currentUserId).stream()
                .map(FavouriteProfileDTO::getProfileId)
                .collect(Collectors.toCollection(HashSet::new));
        Set<String> interestProfileIds = interestTrackService.getSentInterests(currentUserId).stream()
                .map(FavouriteProfileDTO::getProfileId)
                .collect(Collectors.toCollection(HashSet::new));

        List<ProfileCardDTO> userProfiles = baseProfiles.stream()
                .map(dto -> {
                    ProfileCardDTO newDto = new ProfileCardDTO();
                    BeanUtils.copyProperties(dto, newDto);
                    newDto.setFavorited(favoriteProfileIds.contains(dto.getProfileId()));
                    newDto.setInterested(interestProfileIds.contains(dto.getProfileId()));
                    newDto.setBadge(subscriptionManagerService.getUserBadge(dto.getUserId())); // Add badge
                    return newDto;
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(userProfiles);
    }

    @GetMapping("/{profileId}")
    @Cacheable(value = "profiles", key = "#profileId")
    public ResponseEntity<ProfileCardDTO> getUserProfileByProfileId(@PathVariable String profileId) {
        String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();


        // Track profile visit
        subscriptionManagerService.trackProfileVisit(currentUserId, profileId);

        logger.info("Fetching user profile for profileId: {}", profileId);
        Optional<Profile> profileOpt = profileService.getProfileByProfileId(profileId);
        if (!profileOpt.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        Profile profile = profileOpt.get();
        User user = userService.getUserById(profile.getUserId())
                .orElseThrow(() -> new IllegalStateException("User not found for profile: " + profileId));

        ProfileCardDTO baseDto = populateProfileCardDTOAsync(user, profile).join();

        Set<String> favoriteProfileIds = favouriteTracksService.getAllFavourites(currentUserId).stream()
                .map(FavouriteProfileDTO::getProfileId)
                .collect(Collectors.toCollection(HashSet::new));
        Set<String> interestProfileIds = interestTrackService.getSentInterests(currentUserId).stream()
                .map(FavouriteProfileDTO::getProfileId)
                .collect(Collectors.toCollection(HashSet::new));

        ProfileCardDTO dto = new ProfileCardDTO();
        BeanUtils.copyProperties(baseDto, dto);
        dto.setFavorited(favoriteProfileIds.contains(profileId));
        dto.setInterested(interestProfileIds.contains(profileId));
        dto.setBadge(subscriptionManagerService.getUserBadge(user.getId())); // Add badge

        // Restrict contact details
        if (subscriptionManagerService.restrictContactDetails(user.getId())) {
            dto.setEmail(null);

        }

        return ResponseEntity.ok(dto);
    }

    @GetMapping("/me")
    public ResponseEntity<ProfileCardDTO> getMyUserProfile() {
        logger.info("Fetching authenticated user's profile");
        String userId = SecurityContextHolder.getContext().getAuthentication().getName();
        logger.debug("Authenticated userId: {}", userId);

        User user = userService.getUserById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found for ID: " + userId));
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user: " + userId));

        ProfileCardDTO baseDto = populateProfileCardDTOAsync(user, profile).join();

        Set<String> favoriteProfileIds = favouriteTracksService.getAllFavourites(userId).stream()
                .map(FavouriteProfileDTO::getProfileId)
                .collect(Collectors.toCollection(HashSet::new));
        Set<String> interestProfileIds = interestTrackService.getSentInterests(userId).stream()
                .map(FavouriteProfileDTO::getProfileId)
                .collect(Collectors.toCollection(HashSet::new));

        ProfileCardDTO dto = new ProfileCardDTO();
        BeanUtils.copyProperties(baseDto, dto);
        dto.setFavorited(favoriteProfileIds.contains(profile.getProfileId()));
        dto.setInterested(interestProfileIds.contains(profile.getProfileId()));
        dto.setBadge(subscriptionManagerService.getUserBadge(userId)); // Add badge

        return ResponseEntity.ok(dto);
    }

    @GetMapping("/recent-brides")
    public ResponseEntity<List<ProfileCardDTO>> getRecentBrides(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "8") int size) {
        String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();

        logger.info("Fetching recent bride profiles, page: {}, size: {}", page, size);
        return getRecentProfiles("Female", page, size);
    }

    @GetMapping("/recent-grooms")
    public ResponseEntity<List<ProfileCardDTO>> getRecentGrooms(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "8") int size) {
        String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();

        logger.info("Fetching recent groom profiles, page: {}, size: {}", page, size);
        return getRecentProfiles("Male", page, size);
    }

    @GetMapping("/all-brides")
    public ResponseEntity<List<ProfileCardDTO>> getAllBrides() {
        String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();

        logger.info("Fetching all bride profiles");
        return getAllProfiles("Female");
    }

    @GetMapping("/all-grooms")
    public ResponseEntity<List<ProfileCardDTO>> getAllGrooms() {
        String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();

        logger.info("Fetching all groom profiles");
        return getAllProfiles("Male");
    }

    @GetMapping("/lifestyle/{profileId}")
    public ResponseEntity<Lifestyle> getLifestyleTags(@PathVariable String profileId) {
        logger.info("Fetching lifestyle tags for profileId: {}", profileId);
        return ResponseEntity.ok(lifestyleService.getLifestyleTagsByProfileId(profileId));
    }

    private ResponseEntity<List<ProfileCardDTO>> getRecentProfiles(String gender, int page, int size) {
        logger.info("Fetching recent profiles for gender: {}, page: {}, size: {}", gender, page, size);
        Pageable pageable = PageRequest.of(page, size);
        List<User> users = userService.findByStatus("Active", pageable);
        logger.debug("Found {} active users", users.size());

        Map<String, Profile> profileMap = profileService.getProfilesByUserIds(
                users.stream().map(User::getId).collect(Collectors.toList())
        );

        List<String> profileIds = profileMap.values().stream()
                .map(Profile::getProfileId)
                .collect(Collectors.toList());
        List<Lifestyle> lifestyles = lifestyleService.getLifestyleByProfileIds(profileIds);

        String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        List<ProfileCardDTO> baseProfiles = users.parallelStream()
                .filter(user -> profileMap.containsKey(user.getId()))
                .map(user -> populateProfileCardDTOAsync(user, profileMap.get(user.getId())))
                .map(CompletableFuture::join)
                .filter(dto -> dto != null && gender.equalsIgnoreCase(dto.getGender()))

                .peek(dto -> {
                    lifestyles.stream()
                            .filter(l -> l.getProfileId().equals(dto.getProfileId()))
                            .findFirst()
                            .ifPresent(lifestyle -> {
                                List<String> preferences = lifestyle.getPreferences() != null ?
                                        lifestyle.getPreferences().stream()
                                                .map(Object::toString)
                                                .limit(3)
                                                .collect(Collectors.toList()) :
                                        Collections.emptyList();
                                dto.setLifestylePreferences(preferences);
                            });
                    dto.setBadge(subscriptionManagerService.getUserBadge(dto.getUserId())); // Add badge
                })
                .collect(Collectors.toList());

        Set<String> favoriteProfileIds = favouriteTracksService.getAllFavourites(currentUserId).stream()
                .map(FavouriteProfileDTO::getProfileId)
                .collect(Collectors.toCollection(HashSet::new));
        Set<String> interestProfileIds = interestTrackService.getSentInterests(currentUserId).stream()
                .map(FavouriteProfileDTO::getProfileId)
                .collect(Collectors.toCollection(HashSet::new));

        List<ProfileCardDTO> profiles = baseProfiles.stream()
                .map(dto -> {
                    ProfileCardDTO newDto = new ProfileCardDTO();
                    BeanUtils.copyProperties(dto, newDto);
                    newDto.setFavorited(favoriteProfileIds.contains(dto.getProfileId()));
                    newDto.setInterested(interestProfileIds.contains(dto.getProfileId()));
                    newDto.setBadge(dto.getBadge()); // Preserve badge
                    return newDto;
                })
                .collect(Collectors.toList());

        logger.info("Returning {} profiles for gender: {}", profiles.size(), gender);
        if (profiles.isEmpty()) {
            logger.warn("No profiles found for gender: {}. Check ProfileInfo.gender and Profile.lastSeen data.", gender);
        }
        return ResponseEntity.ok(profiles);
    }

    private ResponseEntity<List<ProfileCardDTO>> getAllProfiles(String gender) {
        logger.info("Fetching all profiles for gender: {}", gender);
        List<User> users = userService.filterByStatus("Active");
        Map<String, Profile> profileMap = profileService.getProfilesByUserIds(
                users.stream().map(User::getId).collect(Collectors.toList())
        );

        List<String> profileIds = profileMap.values().stream()
                .map(Profile::getProfileId)
                .collect(Collectors.toList());
        List<Lifestyle> lifestyles = lifestyleService.getLifestyleByProfileIds(profileIds);

        String currentUserId = SecurityContextHolder.getContext().getAuthentication().getName();
        List<ProfileCardDTO> baseProfiles = users.parallelStream()
                .filter(user -> profileMap.containsKey(user.getId()))
                .map(user -> populateProfileCardDTOAsync(user, profileMap.get(user.getId())))
                .map(CompletableFuture::join)
                .filter(dto -> gender.equalsIgnoreCase(dto.getGender()))
                .filter(dto -> dto.getFirstName() != null && dto.getGender() != null) // Filter invalid DTOs
                .peek(dto -> {
                    lifestyles.stream()
                            .filter(l -> l.getProfileId().equals(dto.getProfileId()))
                            .findFirst()
                            .ifPresent(lifestyle -> {
                                List<String> preferences = lifestyle.getPreferences() != null ?
                                        lifestyle.getPreferences().stream()
                                                .map(Object::toString)
                                                .limit(3)
                                                .collect(Collectors.toList()) :
                                        Collections.emptyList();
                                dto.setLifestylePreferences(preferences);
                            });
                    dto.setBadge(subscriptionManagerService.getUserBadge(dto.getUserId())); // Add badge
                })
                .collect(Collectors.toList());

        Set<String> favoriteProfileIds = favouriteTracksService.getAllFavourites(currentUserId).stream()
                .map(FavouriteProfileDTO::getProfileId)
                .collect(Collectors.toCollection(HashSet::new));
        Set<String> interestProfileIds = interestTrackService.getSentInterests(currentUserId).stream()
                .map(FavouriteProfileDTO::getProfileId)
                .collect(Collectors.toCollection(HashSet::new));

        List<ProfileCardDTO> profiles = baseProfiles.stream()
                .map(dto -> {
                    ProfileCardDTO newDto = new ProfileCardDTO();
                    BeanUtils.copyProperties(dto, newDto);
                    newDto.setFavorited(favoriteProfileIds.contains(dto.getProfileId()));
                    newDto.setInterested(interestProfileIds.contains(dto.getProfileId()));
                    newDto.setBadge(dto.getBadge()); // Preserve badge
                    return newDto;
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(profiles);
    }

    private CompletableFuture<ProfileCardDTO> populateProfileCardDTOAsync(User user, Profile profile) {
        ProfileCardDTO dto = new ProfileCardDTO();
        dto.setUserId(user.getId());
        dto.setProfileId(profile.getProfileId());
        dto.setActive(profile.getActive());
        dto.setLastSeen(profile.getLastSeen());
        dto.setEmail(user.getEmail()); // Add email


        CompletableFuture<Void> profileInfoFuture = CompletableFuture.runAsync(() ->
                profileInfoService.getProfileInfoByProfileId(profile.getProfileId()).ifPresent(info -> {
                    dto.setFirstName(info.getFirstName());
                    dto.setGender(info.getGender());
                    dto.setAge(calculateAge(info.getDob()));
                    dto.setLocation(buildLocation(info.getCountry(), info.getState(), info.getCity()));
                })).exceptionally(throwable -> {
            logger.error("Failed to fetch profile info for profileId: {}", profile.getProfileId(), throwable);
            return null;
        });

        CompletableFuture<Void> imagesFuture = CompletableFuture.runAsync(() ->
                profileImagesService.getProfileImagesByProfileId(profile.getProfileId()).ifPresent(images -> {
                    dto.setProfileImage(images.getPrimaryImage() != null ? images.getPrimaryImage() :
                            (!images.getImages().isEmpty() ? images.getImages().get(0) : null));
                })).exceptionally(throwable -> {
            logger.error("Failed to fetch images for profileId: {}", profile.getProfileId(), throwable);
            return null;
        });

        CompletableFuture<Void> carrierFuture = CompletableFuture.runAsync(() ->
                carrierService.getCarrierByProfileId(profile.getProfileId()).ifPresent(carrier -> {
                    dto.setDesignation(carrier.getDesignation());
                })).exceptionally(throwable -> {
            logger.error("Failed to fetch carrier for profileId: {}", profile.getProfileId(), throwable);
            return null;
        });

        return CompletableFuture.allOf(profileInfoFuture, imagesFuture, carrierFuture)
                .thenApply(v -> dto)
                .exceptionally(throwable -> {
                    logger.error("Failed to populate DTO for userId: {}", user.getId(), throwable);
                    return dto;
                });
    }

    private String buildLocation(String country, String state, String city) {
        StringBuilder location = new StringBuilder();
        if (country != null && !country.trim().isEmpty()) {
            location.append(country);
        }
        if (state != null && !state.trim().isEmpty()) {
            if (location.length() > 0) location.append(", ");
            location.append(state);
        }
        if (city != null && !city.trim().isEmpty()) {
            if (location.length() > 0) location.append(", ");
            location.append(city);
        }
        return location.length() > 0 ? location.toString() : null;
    }

    private Integer calculateAge(String dob) {
        if (dob == null) {
            logger.error("DOB is null");
            return null;
        }

        String yyyyMMddPattern = "\\d{4}-\\d{2}-\\d{2}";
        String ddMMyyyyPattern = "\\d{2}-\\d{2}-\\d{4}";
        String ddMMyyPattern = "\\d{2}/\\d{2}/\\d{2}";
        String ddMMyyyySlashPattern = "\\d{2}/\\d{2}/\\d{4}";

        try {
            if (dob.matches(ddMMyyyyPattern)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
                LocalDate birthDate = LocalDate.parse(dob, formatter);
                return Period.between(birthDate, LocalDate.now()).getYears();
            } else if (dob.matches(yyyyMMddPattern)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate birthDate = LocalDate.parse(dob, formatter);
                return Period.between(birthDate, LocalDate.now()).getYears();
            } else if (dob.matches(ddMMyyPattern)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yy");
                LocalDate birthDate = LocalDate.parse(dob, formatter);
                return Period.between(birthDate, LocalDate.now()).getYears();
            } else if (dob.matches(ddMMyyyySlashPattern)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                LocalDate birthDate = LocalDate.parse(dob, formatter);
                return Period.between(birthDate, LocalDate.now()).getYears();
            } else {
                logger.error("Invalid DOB format: {}", dob);
                return null;
            }
        } catch (DateTimeParseException e) {
            logger.error("Error parsing DOB: {}", dob, e);
            return null;
        }
    }
}