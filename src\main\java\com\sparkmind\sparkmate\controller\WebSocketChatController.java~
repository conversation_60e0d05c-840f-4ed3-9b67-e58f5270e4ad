package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.ChatMessage;
import com.sparkmind.sparkmate.service.ChatMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

import java.security.Principal;

@Controller
public class WebSocketChatController {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private ChatMessageService chatMessageService;

    @MessageMapping("/chat/{sessionId}")
    public void sendMessage(@DestinationVariable String sessionId, @Payload ChatMessage message, Principal principal) {
        System.out.println("Message received - Principal: " + (principal != null ? principal.getName() : "null"));
        if (principal == null) {
            throw new IllegalStateException("User not authenticated");
        }
        String senderUserId = principal.getName();
        message.setSenderUserId(senderUserId);
        message.setSessionId(sessionId);
        ChatMessage savedMessage = chatMessageService.sendMessage(sessionId, senderUserId, message.getContent(), message.getMessageType());
        messagingTemplate.convertAndSend("/topic/messages/" + sessionId, savedMessage);
    }
}