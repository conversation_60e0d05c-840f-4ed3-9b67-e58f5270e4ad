package com.sparkmind.sparkmate.controller;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import com.sparkmind.sparkmate.model.User;
import com.sparkmind.sparkmate.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@RequestMapping("/api")
public class WelcomeController {

    @Autowired
    private UserService userService;

    @GetMapping("/welcome")
    public ResponseEntity<String> welcome() throws FirebaseAuthException {
        String uid = SecurityContextHolder.getContext().getAuthentication().getName();
        Optional<User> userOpt = userService.getUserById(uid);
        if (!userOpt.isPresent()) {
            // Sync only if not present
            User userToSync = new User();
            userToSync.setId(uid);
            UserRecord firebaseUser = FirebaseAuth.getInstance().getUser(uid);
            userToSync.setEmail(firebaseUser.getEmail());
            userToSync.setName(firebaseUser.getDisplayName());
            userToSync.setMobile(firebaseUser.getPhoneNumber());
            userService.syncUserFromFirestore(userToSync);
        }
        String displayName = userService.getUserById(uid).map(User::getName).orElse("Unknown");
        String welcomeMessage = "Welcome, " + (displayName != null ? displayName : "User " + uid);
        return ResponseEntity.ok(welcomeMessage);
    }
}