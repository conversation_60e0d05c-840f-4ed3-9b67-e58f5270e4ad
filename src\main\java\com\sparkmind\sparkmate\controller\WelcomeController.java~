package com.sparkmind.sparkmate.controller;

import com.sparkmind.sparkmate.model.User;
import com.sparkmind.sparkmate.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@RequestMapping("/api")
public class WelcomeController {

    @Autowired
    private UserService userService;

    @GetMapping("/welcome")
    public ResponseEntity<String> welcome() {
        String uid = SecurityContextHolder.getContext().getAuthentication().getName();
        Optional<User> userOpt = userService.getUserById(uid);
        String displayName = userOpt.map(User::getName).orElse("User " + uid);
        String welcomeMessage = "Welcome, " + displayName;
        return ResponseEntity.ok(welcomeMessage);
    }
}