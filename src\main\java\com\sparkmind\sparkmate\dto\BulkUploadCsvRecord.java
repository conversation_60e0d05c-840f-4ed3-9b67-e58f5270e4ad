package com.sparkmind.sparkmate.dto;

import com.opencsv.bean.CsvBindByName;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

@Data
public class BulkUploadCsvRecord {
    @CsvBindByName(column = "userId", required = false)
    private String userId;

    @CsvBindByName(column = "email", required = true)
    private String email;

    @CsvBindByName(column = "name", required = true)
    private String name;

    @CsvBindByName(column = "mobile", required = false)
    private String mobile;

    @CsvBindByName(column = "role", required = false)
    private String role = "customer";

    @CsvBindByName(column = "isMarriageBureau", required = false)
    private Boolean isMarriageBureau = true; // Changed to true for bulk upload context

    @CsvBindByName(column = "firstName", required = false)
    private String firstName;

    @CsvBindByName(column = "lastName", required = false)
    private String lastName;

    @CsvBindByName(column = "gender", required = false)
    private String gender;

    @CsvBindByName(column = "dob", required = false)
    private String dob;

    @CsvBindByName(column = "maritalStatus", required = false)
    private String maritalStatus;

    @CsvBindByName(column = "religion", required = false)
    private String religion = "Hindu";

    @CsvBindByName(column = "caste", required = false)
    private String caste;

    @CsvBindByName(column = "mangal", required = false)
    private Boolean mangal;

    @CsvBindByName(column = "zodiacSign", required = false)
    private String zodiacSign;

    @CsvBindByName(column = "country", required = false)
    private String country = "India";

    @CsvBindByName(column = "state", required = false)
    private String state;

    @CsvBindByName(column = "city", required = false)
    private String city;

    @CsvBindByName(column = "aboutMe", required = false)
    private String aboutMe;

    @CsvBindByName(column = "lookingFor", required = false)
    private String lookingFor;

    @CsvBindByName(column = "imageUrls", required = false)
    private String imageUrls;

    @CsvBindByName(column = "primaryImageUrl", required = false)
    private String primaryImageUrl;

    @CsvBindByName(column = "school", required = false)
    private String school;

    @CsvBindByName(column = "graduation", required = false)
    private String graduation;

    @CsvBindByName(column = "postGraduation", required = false)
    private String postGraduation;

    @CsvBindByName(column = "university", required = false)
    private String university;

    @CsvBindByName(column = "fathersName", required = false)
    private String fathersName;

    @CsvBindByName(column = "mothersName", required = false)
    private String mothersName;

    @CsvBindByName(column = "brother", required = false)
    private String brother;

    @CsvBindByName(column = "sister", required = false)
    private String sister;

    @CsvBindByName(column = "fathersOccupation", required = false)
    private String fathersOccupation;

    @CsvBindByName(column = "mothersOccupation", required = false)
    private String mothersOccupation;

    @CsvBindByName(column = "familyLocation", required = false)
    private String familyLocation;

    @CsvBindByName(column = "familyInfo", required = false)
    private String familyInfo;

    @CsvBindByName(column = "occupation", required = false)
    private String occupation;

    @CsvBindByName(column = "organizationName", required = false)
    private String organizationName;

    @CsvBindByName(column = "department", required = false)
    private String department;

    @CsvBindByName(column = "employedIn", required = false)
    private String employedIn;

    @CsvBindByName(column = "designation", required = false)
    private String designation;

    @CsvBindByName(column = "earnings", required = false)
    private String earnings;

    @CsvBindByName(column = "farmland", required = false)
    private String farmland;

    @CsvBindByName(column = "lifestylePreferences", required = false)
    private String lifestylePreferences;

    @CsvBindByName(column = "phoneNumber", required = false)
    private String phoneNumber;

    @CsvBindByName(column = "secondaryMobileNo", required = false)
    private String secondaryMobileNo;

    @CsvBindByName(column = "instaId", required = false)
    private String instaId;

    @CsvBindByName(column = "linkedIn", required = false)
    private String linkedIn;

    @CsvBindByName(column = "additionalInfo", required = false)
    private String additionalInfo;

    public List<String> getImageUrlsList() {
        return imageUrls != null && !imageUrls.isEmpty() ? Arrays.asList(imageUrls.split(",")) : null;
    }

    public List<String> getLifestylePreferencesList() {
        return lifestylePreferences != null && !lifestylePreferences.isEmpty() ? Arrays.asList(lifestylePreferences.split(",")) : null;
    }
}