package com.sparkmind.sparkmate.dto;

import lombok.Data;

@Data
public class BulkUploadResult {
    private String email;
    private String userId;
    private String name;
    private String profileId;
    private boolean success;
    private String errorMessage;

    public BulkUploadResult() {}

    public BulkUploadResult(String email, String userId, boolean success, String errorMessage, String name, String profileId) {
        this.email = email;
        this.userId = userId;
        this.success = success;
        this.errorMessage = errorMessage;
        this.name = name;
        this.profileId = profileId;
    }
}