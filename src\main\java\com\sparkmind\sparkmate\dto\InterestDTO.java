package com.sparkmind.sparkmate.dto;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class InterestDTO {
    private String senderUserId;
    private String receiverProfileId;
    private LocalDateTime interestCreatedOn;

    public InterestDTO(String senderUserId, String receiverProfileId, LocalDateTime interestCreatedOn) {
        this.senderUserId = senderUserId;
        this.receiverProfileId = receiverProfileId;
        this.interestCreatedOn = interestCreatedOn;
    }
}