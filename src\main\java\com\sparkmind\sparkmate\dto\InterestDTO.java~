package com.sparkmind.sparkmate.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class InterestDTO {
    private String senderProfileId;
    private String receiverProfileId;
    private LocalDateTime interestCreatedOn;

    public InterestDTO(String senderProfileId, String receiverProfileId, LocalDateTime interestCreatedOn) {
        this.senderProfileId = senderProfileId;
        this.receiverProfileId = receiverProfileId;
        this.interestCreatedOn = interestCreatedOn;
    }
}