package com.sparkmind.sparkmate.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ProfileCardDTO {
    private String userId;
    private String profileId;
    private String firstName;
    private String gender;
    private Integer age;
    private String designation;
    private String location;
    private String profileImage;
    private List<String> lifestylePreferences;
    private boolean favorited;
    private boolean interested;
    private Boolean active;
    private LocalDateTime lastSeen;
    private String badge; // New field for badge
    private String email; // Add email

}