package com.sparkmind.sparkmate.dto;

import com.sparkmind.sparkmate.model.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class ProfileDataDTO {
    @NotNull(message = "Profile is required")
    private Profile profile;

    private FamilyDetails familyDetails;
    private Carrier carrierDetails;
    private Lifestyle lifestyleDetails;
    private ProfileInfo profileInfo;
    private ProfileEducationDetails educationDetails;
    private ProfileImages profileImage;
    private Contact contact;

    // Added for favorite tracking
    private FavouriteTracks favouriteTracks; // Full favorite record (optional)
    private Boolean isFavorited; // Simple flag to indicate if favorited by authenticated user
}