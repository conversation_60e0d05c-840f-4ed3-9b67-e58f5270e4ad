package com.sparkmind.sparkmate.dto;

import com.sparkmind.sparkmate.model.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class ProfileDataDTO {
    @NotNull(message = "Profile is required")
    private Profile profile;

    private FamilyDetails familyDetails;
    private Carrier carrierDetails;
    private Lifestyle lifestyleDetails;
    private ProfileInfo profileInfo; // Updated fields reflected here
    private ProfileEducationDetails educationDetails;
    private ProfileImages profileImage;
    private Contact contact;
    //private MultipartFile[] images;
}