package com.sparkmind.sparkmate.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class UserProfileDTO {
    private String userId;
    private String profileId;
    private String name;
    private String email;
    private String mobile;
    private String lookingFor;
    private String profileImage;
    private List<String> allImages;
    private String firstName;
    private String lastName;
    private String gender;
    private String dob;
    private String religion;
    private String caste;
    private String maritalStatus;
    private String description;
    private Boolean active;
    private LocalDateTime lastSeen;
    private Integer age;
    private String designation;
    private String location;
}