package com.sparkmind.sparkmate.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserProfileLiteDTO {
    private String userId;
    private String profileId;
    private String firstName;
    private String gender; // Added for filtering
    private Integer age;
    private String designation;
    private String location;
    private String profileImage;
    private boolean favorited;
    private boolean interested;
    private Boolean active;
    private LocalDateTime lastSeen;
}