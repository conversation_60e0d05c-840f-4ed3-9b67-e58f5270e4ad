package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "carriers")
public class Carrier {
    @Id
    private String id;
    @Indexed(unique = true)
    private String profileId;
    private String occupation; // From Dropdown: Business, Self Employed, Government Servant, Other
    private String organizationName; // Organisation
    private String department; // New field, Dropdown: IT, Agri, Police, Municipal, …, Other
    private String employedIn;
    private String designation; // New field
    private String earnings; // Number field, stored as String for flexibility
    private String farmland; // New field
}