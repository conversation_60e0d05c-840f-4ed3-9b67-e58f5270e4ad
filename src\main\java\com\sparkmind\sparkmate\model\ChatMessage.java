package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "chatMessages")
public class ChatMessage {

    @Id
    private String messageId;

    private String sessionId;

    private String senderUserId;
    private String senderName;

    private String recipientUserId; // Added for direct messaging

    private String content;

    private LocalDateTime timestamp;

    private String messageType;

    private boolean readStatus;
}