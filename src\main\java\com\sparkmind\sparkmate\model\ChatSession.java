package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Document(collection = "chatSessions")
public class ChatSession {

    @Id
    private String sessionId;

    private String user1Id;
    private String user1Name; // Added for display

    private String user2Id;
    private String user2Name; // Added for display

    private LocalDateTime startTime;

    private LocalDateTime lastActiveTime;

    private boolean active;

    private List<String> blockedUsers = new ArrayList<>();
}