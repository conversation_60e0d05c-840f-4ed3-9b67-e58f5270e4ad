// com/sparkmind/sparkmate/model/ChatSession.java
package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

@Data
@Document(collection = "chatSessions")
public class ChatSession {

    @Id
    private String sessionId;

    private String user1Id;

    private String user2Id;

    private LocalDateTime startTime;

    private LocalDateTime lastActiveTime;

    private boolean active;

    private List<String> blockedUsers = new ArrayList<>(); // Initialize blockedUsers list
}