package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.Size;

@Data
@Document(collection = "contacts")
public class Contact {
    @Id
    private String id;
    @Indexed(unique = true)
    private String profileId;
    private String phoneNumber; // Primary Mobile No
    private String secondaryMobileNo; // New field
    private String instaId; // New field
    private String linkedIn; // New field
    @Size(max = 1000, message = "Additional Info must be 1000 characters or less")
    private String additionalInfo;
}