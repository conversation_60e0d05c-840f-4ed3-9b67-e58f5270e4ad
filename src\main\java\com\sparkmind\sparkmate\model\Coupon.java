package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import java.time.LocalDateTime;

@Data
@Document(collection = "coupons")
public class Coupon {
    @Id
    @Indexed(unique = true)
    private String couponId;

    @NotBlank(message = "Coupon code is required")
    @Indexed(unique = true)
    private String couponCode; // e.g., "SAVE20"

    @Positive(message = "Discount percentage must be positive")
    private Double discountPercentage; // e.g., 20.0

    private Integer maxUsageLimit; // e.g., 100 (null means unlimited)

    private Integer currentUsageCount = 0; // Tracks usage

    private Boolean active = true; // Active or inactive

    private LocalDateTime createdAt;

    private LocalDateTime lastUpdated;

    private String createdBy; // User ID of creator (e.g., ADMIN)
}