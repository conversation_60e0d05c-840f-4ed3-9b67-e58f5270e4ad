package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.Size;

@Data
@Document(collection = "family_details")
public class FamilyDetails {
    @Id
    private String id;
    @Indexed(unique = true)
    private String profileId;
    private String fathersName;
    private String mothersName;
    private String brother; // Dropdown: 1-6
    private String sister; // Dropdown: 1-7
    private String fathersOccupation; // Dropdown: Business, Self Employed, Government Servant, Other
    private String mothersOccupation; // Dropdown: Business, Self Employed, Government Servant, Housewife, Other
    private String familyLocation;
    @Size(max = 1000, message = "More About Family must be 1000 characters or less")
    private String familyInfo; // More About Family
}