package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "favourite_tracks")
@CompoundIndex(def = "{'senderUserId': 1, 'receiverProfileId': 1}", unique = true)
public class FavouriteTracks {
    @Id
    private String id;
    private String senderUserId; // UserId of the user adding the favorite (from Firebase Auth)
    private String receiverProfileId; // Profile.profileId of the favorited profile
}