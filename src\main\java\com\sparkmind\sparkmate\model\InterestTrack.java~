package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "interest_tracking")
@CompoundIndex(def = "{'senderProfileId': 1, 'receiverProfileId': 1}", unique = true)
public class InterestTrack {
    @Id
    private String id;
    private String senderProfileId;
    private String receiverProfileId;
    private LocalDateTime interestCreatedOn;
    private LocalDateTime interestLastUpdatedOn;
}