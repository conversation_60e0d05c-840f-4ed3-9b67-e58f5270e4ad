package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Data
@Document(collection = "lifestyle")
public class Lifestyle {
    @Id
    private String id;
    @Indexed(unique = true)
    private String profileId;

    private List<String> preferences; // Stores all hashtag selections (e.g., ["FitnessEnthusiast", "TravelAddict"])
}