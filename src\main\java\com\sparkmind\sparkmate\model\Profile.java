package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "profiles")
public class Profile {
    @Id
    @Indexed(unique = true) // Index on profileId, unique
    private String profileId;

    @Indexed(unique = true) // Index on userId, unique
    private String userId;

    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private LocalDateTime lastSeen;
    private LocalDateTime lastLoggedIn;
    private Boolean active = true;
}