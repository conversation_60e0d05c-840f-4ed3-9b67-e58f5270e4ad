package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "profile_education_details")
public class ProfileEducationDetails {
    @Id
    private String id;
    @Indexed(unique = true)
    private String profileId;
    private String school;
    private String graduation;
    private String postGraduation;
    private String university;
}