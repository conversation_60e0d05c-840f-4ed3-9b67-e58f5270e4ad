package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Data
@Document(collection = "profile_images")
public class ProfileImages {
    @Id
    private String id; // MongoDB-generated ID

    @Indexed(unique = true)
    private String profileId; // Reference to Profile.profileId

    private List<String> images = new ArrayList<>(); // List of GCS URLs

    private String primaryImage; // URL of the primary image
}