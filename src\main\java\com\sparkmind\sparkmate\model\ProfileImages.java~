package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Data
@Document(collection = "profile_images")
public class ProfileImages {
    @Id
    private String id; // MongoDB-generated ID

    @Indexed(unique = true)
    private String profileId; // Reference to Profile.profileId

    private List<byte[]> images = new ArrayList<>(); // List of image data as byte arrays

    private byte[] primaryImage; // New field for the primary profile image
}