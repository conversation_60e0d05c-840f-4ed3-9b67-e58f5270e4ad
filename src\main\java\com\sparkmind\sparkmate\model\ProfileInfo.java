package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDate;
import java.time.Period;

@Data
@Document(collection = "profile_info")
public class ProfileInfo {
    @Id
    private String id;

    @Indexed(unique = true) // Index on profileId, unique
    private String profileId;

    @NotNull(message = "First Name is required")
    private String firstName;

    private String lastName;

    @NotNull(message = "Gender is required")
    private String gender;

    @NotNull(message = "DOB is required")
    private String dob;

    private Integer age;

    private String maritalStatus;

    private String religion = "Hindu";

    private String caste;

    private Boolean mangal;

    private String zodiacSign;

    private String country = "India";

    private String state;

    private String city;

    @Size(max = 1000, message = "About Me must be 1000 characters or less")
    private String aboutMe;

    @Size(max = 1000, message = "Looking For must be 1000 characters or less")
    private String lookingFor;

    public void setDob(String dob) {
        this.dob = dob;
        this.age = calculateAge(dob);
    }

    private Integer calculateAge(String dob) {
        if (dob == null) return null;
        try {
            LocalDate birthDate = LocalDate.parse(dob);
            LocalDate currentDate = LocalDate.now();
            return Period.between(birthDate, currentDate).getYears();
        } catch (Exception e) {
            return null;
        }
    }
}