package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.time.Period;

@Data
@Document(collection = "profile_info")
public class ProfileInfo {
    @Id
    private String id;
    private String profileId;
    @NotNull(message = "First Name is required")
    private String firstName;
    private String lastName;
    @NotNull(message = "Gender is required")
    private String gender;
    @NotNull(message = "DOB is required")
    private String dob; // Stored as "YYYY-MM-DD"
    private Integer age; // Auto-populated, freezed
    private String maritalStatus;
    private String religion = "Hindu"; // Frozen
    private String caste;
    private Boolean mangal; // Yes/No as Boolean
    private String zodiacSign;
    private String country = "India"; // Default
    private String state;
    private String city;
    @Size(max = 1000, message = "About Me must be 1000 characters or less")
    private String aboutMe; // Matches frontend
    @Size(max = 1000, message = "Looking For must be 1000 characters or less")
    private String lookingFor;

    // Auto-populate age based on DOB
    public void setDob(String dob) {
        this.dob = dob;
        this.age = calculateAge(dob);
    }

    private Integer calculateAge(String dob) {
        if (dob == null) return null;
        try {
            LocalDate birthDate = LocalDate.parse(dob);
            LocalDate currentDate = LocalDate.now(); // As of March 16, 2025
            return Period.between(birthDate, currentDate).getYears();
        } catch (Exception e) {
            return null; // Handle invalid DOB gracefully
        }
    }
}