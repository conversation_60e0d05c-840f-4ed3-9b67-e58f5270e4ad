package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "profile_matches")
public class ProfileMatch {
    @Id
    private String id; // MongoDB-generated ID

    private String profileId; // Reference to Profile.profileId
    private String matchedProfileId; // Reference to matched Profile.profileId
}