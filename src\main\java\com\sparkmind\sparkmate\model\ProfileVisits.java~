package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "profile_visits")
@CompoundIndex(name = "visitToProfile_visitFromUserId", def = "{'visitToProfile': 1, 'visitFromUserId': 1}", unique = true)
@CompoundIndex(name = "visitToProfile_lastVisitedOn", def = "{'visitToProfile': 1, 'lastVisitedOn': -1}")
public class ProfileVisits {
    @Id
    private String id;
    @Indexed
    private String visitToProfile;
    @Indexed
    private String visitFromUserId;
    private LocalDateTime lastVisitedOn;
    private Integer visitCount;
}