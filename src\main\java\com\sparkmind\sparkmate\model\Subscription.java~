package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "subscriptions")
public class Subscription {
    @Id
    private String subscriptionId;

    @Indexed
    private String userId; // References User.id

    @Indexed
    private String planId; // References SubscriptionPlan.planId

    private String razorpaySubscriptionId; // Razorpay subscription ID

    private LocalDateTime startDate;

    private LocalDateTime endDate; // Null if active

    private String status; // "active", "pending", "cancelled"

    private String transactionId; // From Razorpay payment response

    private String paymentStatus; // "success", "failed"
}