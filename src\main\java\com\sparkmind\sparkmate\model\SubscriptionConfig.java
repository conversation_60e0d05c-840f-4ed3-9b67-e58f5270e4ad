package com.sparkmind.sparkmate.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Document(collection = "subscription_config")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionConfig {

    @Id
    private String id;

    private String planId;

    private int maxProfileVisits;

    private int maxMessages;

    private int durationDays;

    private LocalDateTime createdAt = LocalDateTime.now();

    private LocalDateTime updatedAt = LocalDateTime.now();
}