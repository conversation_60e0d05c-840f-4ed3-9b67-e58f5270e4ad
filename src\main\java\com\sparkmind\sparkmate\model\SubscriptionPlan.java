package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Document(collection = "subscription_plans")
public class SubscriptionPlan {
    @Id
    @Indexed(unique = true)
    private String planId;

    @NotBlank(message = "Plan name is required")
    @Indexed(unique = true)
    private String planName; // e.g., "Basic", "Premium"

    private String description; // e.g., "Access to basic features"

    @Positive(message = "Monthly amount must be positive")
    private Double monthlyAmount; // e.g., 500.0

    @Positive(message = "Annual amount must be positive")
    private Double annualAmount; // e.g., 4800.0 (with discount)

    private Double discountPercentage; // e.g., 20.0 (optional)

    private String period = "monthly"; // "monthly" or "yearly"

    private Integer interval = 1; // 1 for monthly, 12 for yearly

    private List<String> features = new ArrayList<>(); // Store features as a list of strings

    private Boolean active = true; // For soft delete

    private LocalDateTime createdAt; // When the plan was created

    private LocalDateTime lastUpdated; // Track updates
    private String badge;
}