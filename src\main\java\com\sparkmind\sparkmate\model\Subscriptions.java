package com.sparkmind.sparkmate.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "subscriptions")
public class Subscriptions {
    @Id
    private String subscriptionId;

    @Indexed
    private String userId; // References User.id

    @Indexed
    private String planId; // References SubscriptionPlan.planId

    private LocalDateTime startDate;

    private LocalDateTime endDate;

    private String status; // "pending", "active", "failed"

    private String transactionId; // Placeholder for future payment tracking

    private String paymentStatus; // "success", "failed"

    private LocalDateTime createdAt; // Added for creation timestamp

    private LocalDateTime updatedAt; // Added for update timestam

}