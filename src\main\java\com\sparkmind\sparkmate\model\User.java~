package com.sparkmind.sparkmate.model;

import com.google.cloud.Timestamp;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@Data
@Document(collection = "users")
public class User {
    @Id
    @Indexed(unique = true)
    private String id;

    @NotNull(message = "Name cannot be null")
    @NotBlank(message = "Name cannot be empty")
    private String name;

    @NotNull(message = "Email cannot be null")
    @Email(message = "Email must be valid")
    @Indexed(unique = true)
    private String email;

    private String mobile;

    private String password; // Should be hashed in production (e.g., BCrypt)

    @NotNull(message = "Role cannot be null")
    private String role = "user";

    @NotNull(message = "Status cannot be null")
    private String status = "Active";

    private String lookingFor;

    @CreatedDate
    private Timestamp createdAt;

    private List<String> subscriptionIds = new ArrayList<>(); // Tracks subscription IDs
    private List<String> subscriptions; // List of subscription IDs

    private String razorpayCustomerId; // New field: Razorpay customer ID for payment processing

    private Boolean isMarriageBureau; // New field, defaults to false

    private String additionalInfo;

    // Getter/Setter for createdAt as LocalDateTime
    public LocalDateTime getCreatedAtAsLocalDateTime() {
        return createdAt != null ? createdAt.toDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
    }

    public void setCreatedAtAsLocalDateTime(LocalDateTime localDateTime) {
        this.createdAt = localDateTime != null ? Timestamp.of(java.sql.Timestamp.valueOf(localDateTime)) : null;
    }
}