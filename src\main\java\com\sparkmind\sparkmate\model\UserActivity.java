package com.sparkmind.sparkmate.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Document(collection = "user_activity")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserActivity {

    @Id
    private String id;

    private String userId;

    private String subscriptionId;

    private int profileVisitCount = 0;

    private Set<String> visitedProfileIds = new HashSet<>(); // Track unique profile visits

    private LocalDateTime lastVisitTimestamp;

    private int messageCount = 0;

    private Set<String> messagedUserIds = new HashSet<>(); // Track unique message recipients

    private LocalDateTime createdAt = LocalDateTime.now();

    private LocalDateTime updatedAt = LocalDateTime.now();
}