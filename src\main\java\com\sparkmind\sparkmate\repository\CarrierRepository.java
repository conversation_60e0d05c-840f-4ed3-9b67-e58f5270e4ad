package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.Carrier;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface CarrierRepository extends MongoRepository<Carrier, String> {
    List<Carrier> findByProfileId(String profileId);

    @Query("{ 'profileId': ?0 }")
    Optional<Carrier> findFirstByProfileId(String profileId);
}