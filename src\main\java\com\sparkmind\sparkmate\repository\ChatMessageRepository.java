package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.ChatMessage;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.domain.Pageable;
import java.util.List;

public interface ChatMessageRepository extends MongoRepository<ChatMessage, String> {

    List<ChatMessage> findBySessionId(String sessionId, Pageable pageable);

    List<ChatMessage> findBySessionId(String sessionId);
}