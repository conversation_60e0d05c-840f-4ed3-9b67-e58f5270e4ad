package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.ChatRequest;
import org.springframework.data.mongodb.repository.MongoRepository;
import java.util.List;
import java.util.Optional;

public interface ChatRequestRepository extends MongoRepository<ChatRequest, String> {

    List<ChatRequest> findByRecipientUserIdAndStatus(String recipientUserId, String status);

    Optional<ChatRequest> findByRequestId(String requestId);

    Optional<ChatRequest> findByRequesterUserIdAndRecipientUserIdAndStatus(String requesterUserId, String recipientUserId, String status);

    List<ChatRequest> findByRequesterUserIdAndStatus(String requesterUserId, String status);
}