package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.ChatSession;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface ChatSessionRepository extends MongoRepository<ChatSession, String> {

    @Query("{$or: [{'user1Id': ?0, 'user2Id': ?1}, {'user1Id': ?1, 'user2Id': ?0}]}")
    Optional<ChatSession> findByUser1IdAndUser2IdOrUser2IdAndUser1Id(String user1Id, String user2Id);

    @Query("{$or: [{'user1Id': ?0}, {'user2Id': ?0}]}")
    List<ChatSession> findByUser1IdOrUser2Id(String userId);
}