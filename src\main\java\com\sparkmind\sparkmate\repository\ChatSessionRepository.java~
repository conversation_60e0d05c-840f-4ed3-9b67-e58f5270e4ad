package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.ChatSession;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface ChatSessionRepository extends MongoRepository<ChatSession, String> {

    Optional<ChatSession> findByUser1IdAndUser2IdOrUser2IdAndUser1Id(String user1Id, String user2Id);
    List<ChatSession> findByUser1IdOrUser2Id(String user1Id, String user2Id);
}