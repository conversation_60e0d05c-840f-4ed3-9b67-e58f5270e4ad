package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.FavouriteTracks;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface FavouriteTracksRepository extends MongoRepository<FavouriteTracks, String> {
    Optional<FavouriteTracks> findBySenderProfileIdAndReceiverProfileId(String senderProfileId, String receiverProfileId);
    List<FavouriteTracks> findBySenderProfileId(String senderProfileId);
}