package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.InterestTrack;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface InterestTrackRepository extends MongoRepository<InterestTrack, String> {
    Optional<InterestTrack> findBySenderProfileIdAndReceiverProfileId(String senderProfileId, String receiverProfileId);
    List<InterestTrack> findByReceiverProfileId(String receiverProfileId);
    List<InterestTrack> findBySenderProfileId(String senderProfileId);
}