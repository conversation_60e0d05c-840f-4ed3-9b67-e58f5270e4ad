package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.Lifestyle;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface LifestyleRepository extends MongoRepository<Lifestyle, String> {
    List<Lifestyle> findByProfileId(String profileId);

    @Query("{ 'profileId': { $in: ?0 } }")
    List<Lifestyle> findByProfileIdIn(List<String> profileIds);
}