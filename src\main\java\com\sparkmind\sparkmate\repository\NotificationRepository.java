package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.Notification;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface NotificationRepository extends MongoRepository<Notification, String> {
    List<Notification> findByReceiverUserIdOrderByCreatedAtDesc(String receiverUserId);
    List<Notification> findTop4ByReceiverUserIdOrderByCreatedAtDesc(String receiverUserId);
}