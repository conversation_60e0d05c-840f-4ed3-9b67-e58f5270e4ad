package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.ProfileEducationDetails;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface ProfileEducationDetailsRepository extends MongoRepository<ProfileEducationDetails, String> {
    List<ProfileEducationDetails> findByProfileId(String profileId);
    Optional<ProfileEducationDetails> findFirstByProfileId(String profileId);
    void deleteByProfileId(String profileId);
    boolean existsByProfileId(String profileId);
}