package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.ProfileInfo;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Optional;

public interface ProfileInfoRepository extends MongoRepository<ProfileInfo, String> {

    Optional<ProfileInfo> findByProfileId(String profileId);

    // Enhanced for future capability
    Optional<ProfileInfo> findByFirstNameAndLastName(String firstName, String lastName);
}