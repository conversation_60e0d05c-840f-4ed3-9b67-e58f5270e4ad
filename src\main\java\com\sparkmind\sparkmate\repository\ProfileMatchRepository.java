package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.ProfileMatch;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface ProfileMatchRepository extends MongoRepository<ProfileMatch, String> {
    List<ProfileMatch> findByProfileId(String profileId);
    Optional<ProfileMatch> findByProfileIdAndMatchedProfileId(String profileId, String matchedProfileId);
}