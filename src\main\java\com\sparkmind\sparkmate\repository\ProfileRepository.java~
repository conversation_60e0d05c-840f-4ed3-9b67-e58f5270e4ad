package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.Profile;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface ProfileRepository extends MongoRepository<Profile, String> {

    Optional<Profile> findByUserId(String userId);

    Optional<Profile> findByProfileId(String profileId);

    // Enhanced for future capability
    List<Profile> findByActive(Boolean active);

    @Query("{ 'userId': ?0, 'active': ?1 }")
    Optional<Profile> findByUserIdAndActive(String userId, Boolean active);

    @Query("{ 'userId': { $in: ?0 } }")
    List<Profile> findByUserIdIn(List<String> userIds);
}