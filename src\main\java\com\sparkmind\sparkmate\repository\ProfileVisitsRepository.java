package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.ProfileVisits;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ProfileVisitsRepository extends MongoRepository<ProfileVisits, String> {
    @Query(value = "{'visitToProfile' : ?0}")
    List<ProfileVisits> findByVisitToProfile(String visitToProfile);

    Optional<ProfileVisits> findByVisitToProfileAndVisitFromUserId(String visitToProfile, String visitFromUserId);

    @Query(value = "{'visitToProfile': ?0}", sort = "{'lastVisitedOn': -1, '_id': 1}")
    Optional<ProfileVisits> findTopByVisitToProfileOrderByLastVisitedOnDesc(String visitToProfile);

    @Query("{'visitToProfile': ?0, 'lastVisitedOn': {$gt: ?1}}")
    List<ProfileVisits> findByVisitToProfileAndLastVisitedOnAfter(String visitToProfile, LocalDateTime timeRange);

    @Query("{'visitFromUserId': ?0, 'visitToProfile' : {$in : ?1}}")
    List<ProfileVisits> findAllByVisitFromUserIdAndVisitToProfileIn(String visitFromUserId, List<String> visitToProfile);
}