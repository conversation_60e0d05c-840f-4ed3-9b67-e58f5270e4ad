package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.SubscriptionPlan;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface SubscriptionPlanRepository extends MongoRepository<SubscriptionPlan, String> {
    Optional<SubscriptionPlan> findByPlanName(String planName);
    List<SubscriptionPlan> findByActiveTrue();
}