package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.Subscriptions;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface SubscriptionsRepository extends MongoRepository<Subscriptions, String> {
    Optional<Subscriptions> findByUserIdAndStatus(String userId, String status);

    List<Subscriptions> findByStatus(String active);
}