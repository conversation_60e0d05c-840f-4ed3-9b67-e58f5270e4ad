package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.UserActivity;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Optional;

public interface UserActivityRepository extends MongoRepository<UserActivity, String> {
    Optional<UserActivity> findByUserIdAndSubscriptionId(String userId, String subscriptionId);
    Optional<UserActivity> findByUserId(String userId);
}