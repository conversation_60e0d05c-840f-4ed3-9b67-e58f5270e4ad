package com.sparkmind.sparkmate.repository;

import com.sparkmind.sparkmate.model.User;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface UserRepository extends MongoRepository<User, String> {

    Optional<User> findByEmail(String email);

    @Query("{ 'status': ?0 }")
    List<User> findByStatus(String status, Pageable pageable);

    @Query("{ 'role': ?0, 'status': ?1 }")
    List<User> findByRoleAndStatus(String role, String status);

    boolean existsById(String id);
}