package com.sparkmind.sparkmate.security;

import com.google.cloud.firestore.Firestore;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseToken;
import com.sparkmind.sparkmate.model.User;
import com.sparkmind.sparkmate.service.ProfileService;
import com.sparkmind.sparkmate.service.UserService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;
import java.util.Optional;

@Component
public class FirebaseAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(FirebaseAuthenticationFilter.class);

    private final UserService userService;
    private final Firestore firestore; // Kept for potential future use, though not used now
    private final ProfileService profileService;

    @Autowired
    public FirebaseAuthenticationFilter(UserService userService, Firestore firestore, ProfileService profileService) {
        this.userService = userService;
        this.firestore = firestore;
        this.profileService = profileService;
        logger.info("FirebaseAuthenticationFilter initialized");
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String authHeader = request.getHeader("Authorization");

        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            logger.warn("No Bearer token found in Authorization header");
            filterChain.doFilter(request, response);
            return;
        }

        try {
            String idToken = authHeader.substring(7);
            FirebaseToken decodedToken = FirebaseAuth.getInstance().verifyIdToken(idToken);
            String uid = decodedToken.getUid();
            logger.info("Token verified, UID: {}", uid);

            // Check if user exists in DB
            Optional<User> userOpt = userService.getUserById(uid);
            if (!userOpt.isPresent()) {
                logger.info("User {} not found in DB, client should trigger sync", uid);
                response.setHeader("X-Sync-Required", "true"); // Hint to client
            } else {
                User user = userOpt.get();
                if (!"Active".equals(user.getStatus())) {
                    logger.warn("User {} is not active", user.getEmail());
                    response.sendError(HttpServletResponse.SC_FORBIDDEN, "User is not active");
                    return;
                }
            }

            // Set authentication
            UsernamePasswordAuthenticationToken authentication =
                    new UsernamePasswordAuthenticationToken(uid, null, Collections.emptyList());
            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authentication);
            logger.info("Authentication set for UID: {}", uid);

        } catch (Exception e) {
            logger.error("Token verification failed: {}", e.getMessage(), e);
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid Firebase token");
            return;
        }

        filterChain.doFilter(request, response);
    }
}