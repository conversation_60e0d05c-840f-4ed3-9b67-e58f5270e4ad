package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.Carrier;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.repository.CarrierRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class CarrierService {

    private static final Logger logger = LoggerFactory.getLogger(CarrierService.class);

    @Autowired
    private CarrierRepository carrierRepository;

    @Autowired
    private ProfileService profileService;

    public Carrier createCarrier(String userId, Carrier carrier) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));
        carrier.setProfileId(profile.getProfileId());
        logger.info("Creating carrier for profileId: {}", profile.getProfileId());
        return carrierRepository.save(carrier);
    }

    public Optional<Carrier> getCarrierByUserId(String userId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));
        logger.info("Fetching carrier for profileId: {}", profile.getProfileId());
        return carrierRepository.findByProfileId(profile.getProfileId())
                .stream().findFirst();
    }

    @Cacheable(value = "carrier", key = "#profileId")
    public Optional<Carrier> getCarrierByProfileId(String profileId) {
        logger.info("Fetching carrier for profileId: {}", profileId);
        return carrierRepository.findFirstByProfileId(profileId);
    }

    public Carrier updateCarrier(String userId, String carrierId, Carrier updatedCarrier) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        Carrier carrier = carrierRepository.findById(carrierId)
                .filter(c -> c.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new RuntimeException("Carrier not found or not owned by user"));
        carrier.setOccupation(updatedCarrier.getOccupation());
        carrier.setOrganizationName(updatedCarrier.getOrganizationName());
        carrier.setDepartment(updatedCarrier.getDepartment());
        carrier.setEmployedIn(updatedCarrier.getEmployedIn());
        carrier.setDesignation(updatedCarrier.getDesignation());
        carrier.setEarnings(updatedCarrier.getEarnings());
        carrier.setFarmland(updatedCarrier.getFarmland());
        logger.info("Updating carrier id: {} for profileId: {}", carrierId, profile.getProfileId());
        return carrierRepository.save(carrier);
    }

    public void deleteCarrier(String userId, String carrierId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        Carrier carrier = carrierRepository.findById(carrierId)
                .filter(c -> c.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new RuntimeException("Carrier not found or not owned by user"));
        logger.info("Deleting carrier id: {} for profileId: {}", carrierId, profile.getProfileId());
        carrierRepository.delete(carrier);
    }
}