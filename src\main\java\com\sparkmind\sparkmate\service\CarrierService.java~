package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.Carrier;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.repository.CarrierRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class CarrierService {

    private static final Logger logger = LoggerFactory.getLogger(CarrierService.class);

    @Autowired
    private CarrierRepository carrierRepository;

    @Autowired
    private ProfileService profileService;

    @CacheEvict(value = {"carrierByUserId", "carrier"}, allEntries = true)
    public Carrier createCarrier(String userId, Carrier carrier) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));
        carrier.setProfileId(profile.getProfileId());
        logger.debug("Creating carrier for profileId: {}", profile.getProfileId());
        Carrier savedCarrier = carrierRepository.save(carrier);
        logger.info("Created carrier id: {} for profileId: {}", savedCarrier.getId(), profile.getProfileId());
        return savedCarrier;
    }

    @Cacheable(value = "carrierByUserId", key = "#userId", unless = "#result.isEmpty()")
    public Optional<Carrier> getCarrierByUserId(String userId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));
        logger.debug("Fetching carrier for profileId: {}", profile.getProfileId());
        return carrierRepository.findByProfileId(profile.getProfileId())
                .stream().findFirst();
    }

    @Cacheable(value = "carrier", key = "#profileId")
    public Optional<Carrier> getCarrierByProfileId(String profileId) {
        logger.debug("Fetching carrier for profileId: {}", profileId);
        return carrierRepository.findFirstByProfileId(profileId);
    }

    @CacheEvict(value = {"carrierByUserId", "carrier"}, allEntries = true)
    public Carrier updateCarrier(String userId, String carrierId, Carrier updatedCarrier) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        Carrier carrier = carrierRepository.findById(carrierId)
                .filter(c -> c.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new RuntimeException("Carrier not found or not owned by user"));
        carrier.setOccupation(updatedCarrier.getOccupation());
        carrier.setOrganizationName(updatedCarrier.getOrganizationName());
        carrier.setDepartment(updatedCarrier.getDepartment());
        carrier.setEmployedIn(updatedCarrier.getEmployedIn());
        carrier.setDesignation(updatedCarrier.getDesignation());
        carrier.setEarnings(updatedCarrier.getEarnings());
        carrier.setFarmland(updatedCarrier.getFarmland());
        logger.debug("Updating carrier id: {} for profileId: {}", carrierId, profile.getProfileId());
        Carrier updated = carrierRepository.save(carrier);
        logger.info("Updated carrier id: {}", carrierId);
        return updated;
    }

    @CacheEvict(value = {"carrierByUserId", "carrier"}, allEntries = true)
    public void deleteCarrier(String userId, String carrierId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        Carrier carrier = carrierRepository.findById(carrierId)
                .filter(c -> c.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new RuntimeException("Carrier not found or not owned by user"));
        logger.debug("Deleting carrier id: {} for profileId: {}", carrierId, profile.getProfileId());
        carrierRepository.delete(carrier);
        logger.info("Deleted carrier id: {}", carrierId);
    }
}