package com.sparkmind.sparkmate.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sparkmind.sparkmate.model.Subscriptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
public class CashfreeService {
    private static final Logger logger = LoggerFactory.getLogger(CashfreeService.class);

    @Value("${cashfree.app-id}")
    private String appId;

    @Value("${cashfree.secret-key}")
    private String secretKey;

    @Value("${cashfree.api-version}")
    private String apiVersion;

    @Value("${cashfree.environment}")
    private String environment;

    @Value("${cashfree.return-url}")
    private String returnUrl;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    public Map<String, Object> createOrder(Subscriptions subscription, String customerEmail, String customerPhone) {
        try {
            String url = environment.equals("sandbox")
                    ? "https://sandbox.cashfree.com/pg/orders"
                    : "https://api.cashfree.com/pg/orders";

            HttpHeaders headers = new HttpHeaders();
            headers.set("x-client-id", appId);
            headers.set("x-client-secret", secretKey);
            headers.set("x-api-version", apiVersion);
            headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);

            Map<String, Object> payload = Map.of(
                    "order_id", subscription.getSubscriptionId(),
                    "order_amount", getOrderAmount(subscription),
                    "order_currency", "INR",
                    "customer_details", Map.of(
                            "customer_id", subscription.getUserId(),
                            "customer_email", customerEmail,
                            "customer_phone", customerPhone
                    ),
                    "order_meta", Map.of(
                            "return_url", returnUrl.replace("{ORDER_ID}", subscription.getSubscriptionId())
                    )
            );

            String requestBody = objectMapper.writeValueAsString(payload);
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class);

            if (response.getStatusCodeValue() == 200) {
                logger.info("Order created for subscription {}", subscription.getSubscriptionId());
                return response.getBody();
            } else {
                logger.error("Order creation failed: {}", response.getBody());
                throw new RuntimeException("Order creation failed: " + response.getBody());
            }
        } catch (Exception e) {
            logger.error("Error creating order: {}", e.getMessage());
            throw new RuntimeException("Error creating order: " + e.getMessage());
        }
    }

    public Map<String, Object> verifyOrder(String orderId) {
        try {
            String url = environment.equals("sandbox")
                    ? "https://sandbox.cashfree.com/pg/orders/" + orderId
                    : "https://api.cashfree.com/pg/orders/" + orderId;

            HttpHeaders headers = new HttpHeaders();
            headers.set("x-client-id", appId);
            headers.set("x-client-secret", secretKey);
            headers.set("x-api-version", apiVersion);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);

            if (response.getStatusCodeValue() == 200) {
                logger.info("Order verified for orderId {}", orderId);
                return response.getBody();
            } else {
                logger.error("Order verification failed: {}", response.getBody());
                throw new RuntimeException("Order verification failed: " + response.getBody());
            }
        } catch (Exception e) {
            logger.error("Error verifying order: {}", e.getMessage());
            throw new RuntimeException("Error verifying order: " + e.getMessage());
        }
    }

    private double getOrderAmount(Subscriptions subscription) {
        // Logic to fetch amount from SubscriptionPlan
        // Placeholder: Assume monthly amount for now
        return 100.0; // Replace with actual logic
    }
}