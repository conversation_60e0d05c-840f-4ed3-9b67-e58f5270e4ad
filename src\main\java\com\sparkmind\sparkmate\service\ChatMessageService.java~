package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.ChatMessage;
import com.sparkmind.sparkmate.model.ChatSession;
import com.sparkmind.sparkmate.repository.ChatMessageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class ChatMessageService {

    @Autowired
    private ChatMessageRepository chatMessageRepository;

    @Autowired
    private ChatSessionService chatSessionService;

    @Autowired
    private UserService userService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    public ChatMessage sendMessage(String senderUserId, String recipientUserId, String content, String messageType) {
        // Get or create a chat session
        ChatSession session = chatSessionService.getOrCreateChatSession(senderUserId, recipientUserId);
        String sessionId = session.getSessionId();

        if (chatSessionService.isUserBlockedInSession(sessionId, senderUserId)) {
            throw new RuntimeException("You are blocked in this chat session.");
        }

        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setSessionId(sessionId);
        chatMessage.setSenderUserId(senderUserId);
        chatMessage.setContent(content);
        chatMessage.setTimestamp(LocalDateTime.now());
        chatMessage.setMessageType(messageType);
        chatMessage.setReadStatus(false);
        enrichMessageWithSenderDetails(chatMessage);

        ChatMessage savedMessage = chatMessageRepository.save(chatMessage);

        // Broadcast the message
        messagingTemplate.convertAndSend("/topic/messages/" + sessionId, savedMessage);

        return savedMessage;
    }

    public List<ChatMessage> getMessages(String sessionId, int page, int pageSize) {
        Pageable pageable = PageRequest.of(page, pageSize, Sort.by("timestamp").ascending());
        List<ChatMessage> messages = chatMessageRepository.findBySessionId(sessionId, pageable);
        messages.forEach(this::enrichMessageWithSenderDetails);
        return messages;
    }

    public void markMessagesAsRead(String sessionId, String userId) {
        List<ChatMessage> messages = chatMessageRepository.findBySessionId(sessionId);
        for (ChatMessage message : messages) {
            if (!message.getSenderUserId().equals(userId)) {
                message.setReadStatus(true);
                chatMessageRepository.save(message);
            }
        }
    }

    private void enrichMessageWithSenderDetails(ChatMessage message) {
        userService.getUserById(message.getSenderUserId()).ifPresent(user ->
                message.setSenderName(user.getName() != null ? user.getName() : "Unknown User")
        );
    }
}