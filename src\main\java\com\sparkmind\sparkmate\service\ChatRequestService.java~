package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.ChatRequest;
import com.sparkmind.sparkmate.repository.ChatRequestRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class ChatRequestService {

    private static final Logger logger = LoggerFactory.getLogger(ChatRequestService.class);

    @Autowired
    private ChatRequestRepository chatRequestRepository;

    public ChatRequest createChatRequest(String requesterUserId, String recipientUserId, String message) {

        validateChatRequest(requesterUserId, recipientUserId);

        ChatRequest chatRequest = new ChatRequest();
        chatRequest.setRequesterUserId(requesterUserId);
        chatRequest.setRecipientUserId(recipientUserId);
        chatRequest.setStatus("Pending");
        chatRequest.setTimestamp(LocalDateTime.now());
        chatRequest.setMessage(message);
        chatRequest.setExpiryTime(LocalDateTime.now().plusDays(1)); // optional: request expires in 1 day

        return chatRequestRepository.save(chatRequest);
    }

    public ChatRequest acceptChatRequest(String requestId) {
        ChatRequest chatRequest = chatRequestRepository.findByRequestId(requestId)
                .orElseThrow(() -> new RuntimeException("Chat request not found"));

        chatRequest.setStatus("Accepted");
        return chatRequestRepository.save(chatRequest);
    }

    public ChatRequest rejectChatRequest(String requestId) {
        ChatRequest chatRequest = chatRequestRepository.findByRequestId(requestId)
                .orElseThrow(() -> new RuntimeException("Chat request not found"));

        chatRequest.setStatus("Rejected");
        return chatRequestRepository.save(chatRequest);
    }

    public List<ChatRequest> getPendingChatRequests(String recipientUserId) {
        return chatRequestRepository.findByRecipientUserIdAndStatus(recipientUserId, "Pending");
    }

    public List<ChatRequest> getSentChatRequests(String requesterUserId) {
        return chatRequestRepository.findByRequesterUserIdAndStatus(requesterUserId, "Pending");
    }

    public void validateChatRequest(String requesterUserId, String recipientUserId) {
        if (requesterUserId.equals(recipientUserId)) {
            throw new IllegalArgumentException("Cannot send a chat request to yourself.");
        }

        Optional<ChatRequest> existingRequest = chatRequestRepository.findByRequesterUserIdAndRecipientUserIdAndStatus(requesterUserId, recipientUserId, "Pending");
        if(existingRequest.isPresent()){
            throw new IllegalArgumentException("A pending request already exists.");
        }
    }

    public List<ChatRequest> getReceivedChatRequests(String recipientUserId) {
        return chatRequestRepository.findByRecipientUserId(recipientUserId);
    }
}