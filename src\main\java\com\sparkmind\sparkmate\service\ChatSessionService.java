package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.ChatSession;
import com.sparkmind.sparkmate.repository.ChatSessionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class ChatSessionService {

    @Autowired
    private ChatSessionRepository chatSessionRepository;

    @Autowired
    private UserService userService;

    public ChatSession getOrCreateChatSession(String user1Id, String user2Id) {
        Optional<ChatSession> existingSession = chatSessionRepository.findByUser1IdAndUser2IdOrUser2IdAndUser1Id(user1Id, user2Id);
        if (existingSession.isPresent()) {
            return existingSession.get();
        }

        ChatSession chatSession = new ChatSession();
        chatSession.setSessionId(UUID.randomUUID().toString()); // Explicitly set sessionId
        chatSession.setUser1Id(user1Id);
        chatSession.setUser2Id(user2Id);
        chatSession.setStartTime(LocalDateTime.now());
        chatSession.setActive(true);
        chatSession.setBlockedUsers(Collections.emptyList()); // Ensure initialized
        ChatSession savedSession = chatSessionRepository.save(chatSession);
        enrichSessionWithUserDetails(savedSession);
        return savedSession;
    }

    public ChatSession blockUserInSession(String sessionId, String blockedUserId) {
        ChatSession chatSession = chatSessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("Chat session not found"));
        if (!chatSession.getBlockedUsers().contains(blockedUserId)) {
            chatSession.getBlockedUsers().add(blockedUserId);
            chatSessionRepository.save(chatSession);
        }
        enrichSessionWithUserDetails(chatSession);
        return chatSession;
    }

    public ChatSession unblockUserInSession(String sessionId, String unblockedUserId) {
        ChatSession chatSession = chatSessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("Chat session not found"));
        chatSession.getBlockedUsers().remove(unblockedUserId);
        ChatSession updatedSession = chatSessionRepository.save(chatSession);
        enrichSessionWithUserDetails(updatedSession);
        return updatedSession;
    }

    public List<ChatSession> getConversations(String userId) {
        try {
            List<ChatSession> conversations = chatSessionRepository.findByUser1IdOrUser2Id(userId);
            conversations.forEach(this::enrichSessionWithUserDetails);
            return conversations;
        } catch (Exception e) {
            System.err.println("Error fetching conversations: " + e.getMessage());
            e.printStackTrace();
            return Collections.emptyList();
        }
    }

    public boolean isUserBlockedInSession(String sessionId, String userId) {
        ChatSession chatSession = chatSessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("Chat session not found"));
        return chatSession.getBlockedUsers().contains(userId);
    }

    public String getRecipientId(String sessionId, String senderId) {
        ChatSession session = chatSessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("Chat session not found"));
        return session.getUser1Id().equals(senderId) ? session.getUser2Id() : session.getUser1Id();
    }

    private void enrichSessionWithUserDetails(ChatSession session) {
        userService.getUserById(session.getUser1Id()).ifPresent(user ->
                session.setUser1Name(user.getName() != null ? user.getName() : "Unknown User")
        );
        userService.getUserById(session.getUser2Id()).ifPresent(user ->
                session.setUser2Name(user.getName() != null ? user.getName() : "Unknown User")
        );
    }
}