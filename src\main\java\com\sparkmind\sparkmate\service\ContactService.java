package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.Contact;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.repository.ContactRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ContactService {

    @Autowired
    private ContactRepository contactRepository;

    @Autowired
    private ProfileService profileService;

    public Contact createContact(String userId, Contact contact) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));
        contact.setProfileId(profile.getProfileId());
        return contactRepository.save(contact);
    }

    public Optional<Contact> getContactByProfileId(String profileId) {
        return Optional.ofNullable(contactRepository.findByProfileId(profileId));
    }

    public Contact updateContact(String userId, String contactId, Contact updatedContact) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));
        Contact existingContact = contactRepository.findById(contactId)
                .filter(c -> c.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new IllegalArgumentException("Contact not found or not owned by user"));
        existingContact.setPhoneNumber(updatedContact.getPhoneNumber());
        existingContact.setSecondaryMobileNo(updatedContact.getSecondaryMobileNo());
        existingContact.setInstaId(updatedContact.getInstaId());
        existingContact.setLinkedIn(updatedContact.getLinkedIn());
        existingContact.setAdditionalInfo(updatedContact.getAdditionalInfo());
        return contactRepository.save(existingContact);
    }
}