package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.Contact;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.repository.ContactRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ContactService {

    private static final Logger logger = LoggerFactory.getLogger(ContactService.class);

    @Autowired
    private ContactRepository contactRepository;

    @Autowired
    private ProfileService profileService;

    @CacheEvict(value = "contactByProfileId", key = "#contact.profileId")
    public Contact createContact(String userId, Contact contact) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));
        contact.setProfileId(profile.getProfileId());
        logger.debug("Creating contact for profileId: {}", profile.getProfileId());
        Contact savedContact = contactRepository.save(contact);
        logger.info("Created contact id: {} for profileId: {}", savedContact.getId(), profile.getProfileId());
        return savedContact;
    }

    @Cacheable(value = "contactByProfileId", key = "#profileId", unless = "#result.isEmpty()")
    public Optional<Contact> getContactByProfileId(String profileId) {
        logger.debug("Fetching contact for profileId: {}", profileId);
        return Optional.ofNullable(contactRepository.findByProfileId(profileId));
    }

    @CacheEvict(value = "contactByProfileId", key = "#result.profileId")
    public Contact updateContact(String userId, String contactId, Contact updatedContact) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));
        Contact existingContact = contactRepository.findById(contactId)
                .filter(c -> c.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new IllegalArgumentException("Contact not found or not owned by user"));
        existingContact.setPhoneNumber(updatedContact.getPhoneNumber());
        existingContact.setSecondaryMobileNo(updatedContact.getSecondaryMobileNo());
        existingContact.setInstaId(updatedContact.getInstaId());
        existingContact.setLinkedIn(updatedContact.getLinkedIn());
        existingContact.setAdditionalInfo(updatedContact.getAdditionalInfo());
        logger.debug("Updating contact id: {} for profileId: {}", contactId, profile.getProfileId());
        Contact updated = contactRepository.save(existingContact);
        logger.info("Updated contact id: {}", contactId);
        return updated;
    }
}