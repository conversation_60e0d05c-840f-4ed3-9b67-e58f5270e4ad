package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.Coupon;
import com.sparkmind.sparkmate.repository.CouponRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
public class CouponService {
    private static final Logger logger = LoggerFactory.getLogger(CouponService.class);

    private final CouponRepository couponRepository;

    public CouponService(CouponRepository couponRepository) {
        this.couponRepository = couponRepository;
    }

    public Coupon createCoupon(Coupon coupon) {
        if (couponRepository.findByCouponCode(coupon.getCouponCode()).isPresent()) {
            logger.error("Coupon code {} already exists", coupon.getCouponCode());
            throw new RuntimeException("Coupon code already exists");
        }
        coupon.setCouponId(UUID.randomUUID().toString());
        coupon.setCreatedAt(LocalDateTime.now());
        coupon.setLastUpdated(LocalDateTime.now());
        coupon.setCreatedBy("ADMIN"); // Hardcoded for now; integrate authentication later
        logger.info("Creating coupon: {}", coupon.getCouponCode());
        return couponRepository.save(coupon);
    }

    public Coupon getCouponById(String couponId) {
        return couponRepository.findById(couponId)
                .orElseThrow(() -> {
                    logger.error("Coupon not found with ID: {}", couponId);
                    return new RuntimeException("Coupon not found");
                });
    }

    public List<Coupon> getAllActiveCoupons() {
        logger.info("Fetching all active coupons");
        return couponRepository.findByActiveTrue();
    }

    public Coupon updateCoupon(String couponId, Coupon updatedCoupon) {
        Coupon existingCoupon = getCouponById(couponId);
        existingCoupon.setCouponCode(updatedCoupon.getCouponCode());
        existingCoupon.setDiscountPercentage(updatedCoupon.getDiscountPercentage());
        existingCoupon.setMaxUsageLimit(updatedCoupon.getMaxUsageLimit());
        existingCoupon.setActive(updatedCoupon.getActive());
        existingCoupon.setLastUpdated(LocalDateTime.now());
        logger.info("Updating coupon: {}", existingCoupon.getCouponCode());
        return couponRepository.save(existingCoupon);
    }

    public void deleteCoupon(String couponId) {
        Coupon coupon = getCouponById(couponId);
        coupon.setActive(false);
        couponRepository.save(coupon);
        logger.info("Coupon deleted (soft): {}", coupon.getCouponCode());
    }

    public void applyCoupon(String couponCode) {
        Coupon coupon = couponRepository.findByCouponCode(couponCode)
                .orElseThrow(() -> {
                    logger.error("Invalid coupon code: {}", couponCode);
                    return new RuntimeException("Invalid coupon code");
                });
        if (!coupon.getActive() || (coupon.getMaxUsageLimit() != null && coupon.getCurrentUsageCount() >= coupon.getMaxUsageLimit())) {
            logger.error("Coupon {} is inactive or usage limit reached", couponCode);
            throw new RuntimeException("Coupon is inactive or usage limit reached");
        }
        coupon.setCurrentUsageCount(coupon.getCurrentUsageCount() + 1);
        coupon.setLastUpdated(LocalDateTime.now());
        couponRepository.save(coupon);
        logger.info("Coupon {} applied successfully", couponCode);
        // Note: Actual discount application to a subscription plan is not implemented here
    }
}