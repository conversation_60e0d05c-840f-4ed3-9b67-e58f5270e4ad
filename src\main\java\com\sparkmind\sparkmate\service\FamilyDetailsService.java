package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.FamilyDetails;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.repository.FamilyDetailsRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FamilyDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(FamilyDetailsService.class);

    @Autowired
    private FamilyDetailsRepository familyDetailsRepository;

    @Autowired
    private ProfileService profileService;

    public FamilyDetails createFamilyDetails(String userId, FamilyDetails familyDetails) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        familyDetails.setProfileId(profile.getProfileId());
        logger.info("Creating family details for profileId: {}", profile.getProfileId());
        return familyDetailsRepository.save(familyDetails);
    }

    public List<FamilyDetails> getFamilyDetailsByUserId(String userId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        logger.info("Fetching family details for profileId: {}", profile.getProfileId());
        return familyDetailsRepository.findByProfileId(profile.getProfileId());
    }

    public FamilyDetails updateFamilyDetails(String userId, String familyDetailsId, FamilyDetails updatedFamilyDetails) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        FamilyDetails familyDetails = familyDetailsRepository.findById(familyDetailsId)
                .filter(fd -> fd.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new RuntimeException("FamilyDetails not found or not owned by user"));
        familyDetails.setFathersName(updatedFamilyDetails.getFathersName());
        familyDetails.setMothersName(updatedFamilyDetails.getMothersName());
        familyDetails.setBrother(updatedFamilyDetails.getBrother());
        familyDetails.setSister(updatedFamilyDetails.getSister());
        familyDetails.setFathersOccupation(updatedFamilyDetails.getFathersOccupation());
        familyDetails.setMothersOccupation(updatedFamilyDetails.getMothersOccupation());
        familyDetails.setFamilyLocation(updatedFamilyDetails.getFamilyLocation());
        familyDetails.setFamilyInfo(updatedFamilyDetails.getFamilyInfo());
        logger.info("Updating family details id: {} for profileId: {}", familyDetailsId, profile.getProfileId());
        return familyDetailsRepository.save(familyDetails);
    }

    public void deleteFamilyDetails(String userId, String familyDetailsId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        FamilyDetails familyDetails = familyDetailsRepository.findById(familyDetailsId)
                .filter(fd -> fd.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new RuntimeException("FamilyDetails not found or not owned by user"));
        logger.info("Deleting family details id: {} for profileId: {}", familyDetailsId, profile.getProfileId());
        familyDetailsRepository.delete(familyDetails);
    }
}