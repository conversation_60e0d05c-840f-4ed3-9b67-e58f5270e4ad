package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.FamilyDetails;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.repository.FamilyDetailsRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FamilyDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(FamilyDetailsService.class);

    @Autowired
    private FamilyDetailsRepository familyDetailsRepository;

    @Autowired
    private ProfileService profileService;

    @CacheEvict(value = "familyDetailsByUserId", key = "#userId")
    public FamilyDetails createFamilyDetails(String userId, FamilyDetails familyDetails) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        familyDetails.setProfileId(profile.getProfileId());
        logger.debug("Creating family details for profileId: {}", profile.getProfileId());
        FamilyDetails savedFamilyDetails = familyDetailsRepository.save(familyDetails);
        logger.info("Created family details id: {} for profileId: {}", savedFamilyDetails.getId(), profile.getProfileId());
        return savedFamilyDetails;
    }

    @Cacheable(value = "familyDetailsByUserId", key = "#userId", unless = "#result.isEmpty()")
    public List<FamilyDetails> getFamilyDetailsByUserId(String userId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        logger.debug("Fetching family details for profileId: {}", profile.getProfileId());
        return familyDetailsRepository.findByProfileId(profile.getProfileId());
    }

    @CacheEvict(value = "familyDetailsByUserId", key = "#userId")
    public FamilyDetails updateFamilyDetails(String userId, String familyDetailsId, FamilyDetails updatedFamilyDetails) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        FamilyDetails familyDetails = familyDetailsRepository.findById(familyDetailsId)
                .filter(fd -> fd.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new RuntimeException("FamilyDetails not found or not owned by user"));
        familyDetails.setFathersName(updatedFamilyDetails.getFathersName());
        familyDetails.setMothersName(updatedFamilyDetails.getMothersName());
        familyDetails.setBrother(updatedFamilyDetails.getBrother());
        familyDetails.setSister(updatedFamilyDetails.getSister());
        familyDetails.setFathersOccupation(updatedFamilyDetails.getFathersOccupation());
        familyDetails.setMothersOccupation(updatedFamilyDetails.getMothersOccupation());
        familyDetails.setFamilyLocation(updatedFamilyDetails.getFamilyLocation());
        familyDetails.setFamilyInfo(updatedFamilyDetails.getFamilyInfo());
        logger.debug("Updating family details id: {} for profileId: {}", familyDetailsId, profile.getProfileId());
        FamilyDetails updated = familyDetailsRepository.save(familyDetails);
        logger.info("Updated family details id: {}", familyDetailsId);
        return updated;
    }

    @CacheEvict(value = "familyDetailsByUserId", key = "#userId")
    public void deleteFamilyDetails(String userId, String familyDetailsId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        FamilyDetails familyDetails = familyDetailsRepository.findById(familyDetailsId)
                .filter(fd -> fd.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new RuntimeException("FamilyDetails not found or not owned by user"));
        logger.debug("Deleting family details id: {} for profileId: {}", familyDetailsId, profile.getProfileId());
        familyDetailsRepository.delete(familyDetails);
        logger.info("Deleted family details id: {}", familyDetailsId);
    }
}