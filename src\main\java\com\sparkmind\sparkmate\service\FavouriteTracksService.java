package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.dto.FavouriteProfileDTO;
import com.sparkmind.sparkmate.model.FavouriteTracks;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.ProfileImages;
import com.sparkmind.sparkmate.model.ProfileInfo;
import com.sparkmind.sparkmate.repository.FavouriteTracksRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class FavouriteTracksService {

    private static final Logger logger = LoggerFactory.getLogger(FavouriteTracksService.class);

    @Autowired
    private FavouriteTracksRepository favouriteTracksRepository;

    @Autowired
    private ProfileService profileService;


    @Autowired
    private ProfileInfoService profileInfoService;

    @Autowired
    private CarrierService carrierService;

    @Autowired
    private ProfileImagesService profileImagesService;

    public FavouriteTracks addFavourite(String senderUserId, String receiverProfileId) {
        profileService.getProfileByUserId(senderUserId)
                .orElseThrow(() -> new IllegalArgumentException("Sender user profile not found"));
        Profile receiverProfile = profileService.getProfileByProfileId(receiverProfileId)
                .orElseThrow(() -> new IllegalArgumentException("Receiver profile not found"));

        if (favouriteTracksRepository.findBySenderUserIdAndReceiverProfileId(senderUserId, receiverProfileId).isPresent()) {
            throw new IllegalArgumentException("Profile already favorited");
        }

        FavouriteTracks favourite = new FavouriteTracks();
        favourite.setSenderUserId(senderUserId);
        favourite.setReceiverProfileId(receiverProfileId);
        logger.info("Adding favorite: senderUserId {} -> receiverProfileId {}", senderUserId, receiverProfileId);
        return favouriteTracksRepository.save(favourite);
    }

    public void removeFavourite(String senderUserId, String receiverProfileId) {
        FavouriteTracks favourite = favouriteTracksRepository.findBySenderUserIdAndReceiverProfileId(senderUserId, receiverProfileId)
                .orElseThrow(() -> new IllegalArgumentException("Favorite not found"));
        logger.info("Removing favorite: senderUserId {} -> receiverProfileId {}", senderUserId, receiverProfileId);
        favouriteTracksRepository.delete(favourite);
    }

    public List<FavouriteProfileDTO> getAllFavourites(String senderUserId) {
        profileService.getProfileByUserId(senderUserId)
                .orElseThrow(() -> new IllegalArgumentException("Sender user profile not found"));
        logger.info("Fetching all favorites for senderUserId: {}", senderUserId);

        List<FavouriteTracks> favourites = favouriteTracksRepository.findBySenderUserId(senderUserId);
        return favourites.stream()
                .map(this::mapToFavouriteProfileDTO)
                .filter(dto -> dto != null)
                .collect(Collectors.toList());
    }

    // Added method for ProfileDataController
    public Optional<FavouriteTracks> findBySenderUserIdAndReceiverProfileId(String senderUserId, String receiverProfileId) {
        return favouriteTracksRepository.findBySenderUserIdAndReceiverProfileId(senderUserId, receiverProfileId);
    }

    private FavouriteProfileDTO mapToFavouriteProfileDTO(FavouriteTracks favourite) {
        Profile profile = profileService.getProfileByProfileId(favourite.getReceiverProfileId()).orElse(null);
        if (profile == null) {
            logger.warn("No profile found for receiverProfileId: {}", favourite.getReceiverProfileId());
            return null;
        }
        ProfileInfo profileInfo = profileInfoService.getProfileInfoByProfileId(profile.getProfileId()).orElse(null);
        Optional<ProfileImages> profileImagesOpt = profileImagesService.getProfileImagesByProfileId(profile.getProfileId());

        FavouriteProfileDTO dto = new FavouriteProfileDTO();
        dto.setProfileId(profile.getProfileId());
        dto.setUserId(profile.getUserId());
        dto.setFirstName(profileInfo != null ? profileInfo.getFirstName() : "Unknown");
        dto.setAge(profileInfo != null && profileInfo.getDob() != null ? calculateAge(profileInfo.getDob()) : null);
        dto.setGender(profileInfo != null ? profileInfo.getGender() : null);
        dto.setDesignation(carrierService.getCarrierByProfileId(profile.getProfileId())
                .map(c -> c.getDesignation()).orElse(null));
        dto.setProfileImage(profileImagesOpt.map(ProfileImages::getPrimaryImage).orElse(null));
        return dto;
    }

    private Integer calculateAge(String dob) {
        if (dob == null) return null;
        if (!dob.matches("\\d{2}-\\d{2}-\\d{4}")) {
            logger.error("Invalid DOB format: {}", dob);
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
            LocalDate birthDate = LocalDate.parse(dob, formatter);
            return Period.between(birthDate, LocalDate.now()).getYears();
        } catch (Exception e) {
            logger.error("Error parsing dob: {}", dob, e);
            return null;
        }
    }
}