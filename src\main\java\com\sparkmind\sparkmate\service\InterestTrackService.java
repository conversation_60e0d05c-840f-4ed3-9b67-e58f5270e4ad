package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.dto.FavouriteProfileDTO;
import com.sparkmind.sparkmate.dto.InterestDTO;
import com.sparkmind.sparkmate.dto.ProfileInterestDTO;
import com.sparkmind.sparkmate.model.InterestTrack;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.ProfileImages;
import com.sparkmind.sparkmate.model.ProfileInfo;
import com.sparkmind.sparkmate.repository.InterestTrackRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class InterestTrackService {

    private static final Logger logger = LoggerFactory.getLogger(InterestTrackService.class);

    @Autowired
    private InterestTrackRepository repository;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private ProfileInfoService profileInfoService;

    @Autowired
    private CarrierService carrierService;

    @Autowired
    private ProfileImagesService profileImagesService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private MongoTemplate mongoTemplate;

    public InterestTrack sendInterest(String senderUserId, String receiverProfileId) {
        Profile senderProfile = profileService.getProfileByUserId(senderUserId)
                .orElseThrow(() -> new IllegalArgumentException("Sender user not found"));
        Profile receiverProfile = profileService.getProfileByProfileId(receiverProfileId)
                .orElseThrow(() -> new IllegalArgumentException("Receiver profile not found"));

        if (senderProfile.getProfileId().equals(receiverProfileId)) {
            throw new IllegalArgumentException("A user cannot express interest in their own profile");
        }

        InterestTrack interest = new InterestTrack();
        interest.setSenderUserId(senderUserId);
        interest.setReceiverProfileId(receiverProfileId);
        interest.setInterestCreatedOn(LocalDateTime.now());
        interest.setInterestLastUpdatedOn(LocalDateTime.now());

        InterestTrack savedInterest = repository.save(interest);
        notificationService.createInterestNotification(senderUserId, receiverProfile.getUserId());

        logger.info("Interest sent: {} -> {}", senderUserId, receiverProfileId);
        return savedInterest;
    }

    public void removeInterest(String senderUserId, String receiverProfileId) {
        Optional<InterestTrack> interestOpt = repository.findBySenderUserIdAndReceiverProfileId(senderUserId, receiverProfileId);
        if (!interestOpt.isPresent()) {
            throw new IllegalArgumentException("Interest not found or already removed");
        }

        InterestTrack interest = interestOpt.get();
        if (!interest.getSenderUserId().equals(senderUserId)) {
            throw new IllegalArgumentException("You can only remove your own interest");
        }

        repository.delete(interest);
        logger.info("Interest removed: {} -> {}", senderUserId, receiverProfileId);

    }

    public List<FavouriteProfileDTO> getSentInterests(String senderUserId) {
        profileService.getProfileByUserId(senderUserId)
                .orElseThrow(() -> new IllegalArgumentException("Sender user not found"));

        List<InterestTrack> interests = repository.findBySenderUserId(senderUserId);
        return interests.stream()
                .map(interest -> mapToFavouriteProfileDTOByProfileId(interest.getReceiverProfileId()))
                .filter(dto -> dto != null)
                .collect(Collectors.toList());
    }

    public List<FavouriteProfileDTO> getReceivedInterests(String receiverUserId) {
        Profile receiverProfile = profileService.getProfileByUserId(receiverUserId)
                .orElseThrow(() -> new IllegalArgumentException("Receiver user not found"));

        List<InterestTrack> interests = repository.findByReceiverProfileId(receiverProfile.getProfileId());
        return interests.stream()
                .map(interest -> mapToFavouriteProfileDTOByUserId(interest.getSenderUserId()))
                .filter(dto -> dto != null)
                .collect(Collectors.toList());
    }

    public ProfileInterestDTO mostInterested() {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.group("receiverProfileId").count().as("interestCount"),
                Aggregation.sort(org.springframework.data.domain.Sort.Direction.DESC, "interestCount"),
                Aggregation.limit(1)
        );

        AggregationResults<ProfileInterestDTO> results = mongoTemplate.aggregate(
                aggregation, "interest_tracking", ProfileInterestDTO.class);
        ProfileInterestDTO result = results.getUniqueMappedResult();
        if (result != null) {
            logger.info("Most interested profile: {} with count: {}", result.getProfileId(), result.getInterestCount());
        }
        return result;
    }

    public ProfileInterestDTO leastInterested() {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.group("receiverProfileId").count().as("interestCount"),
                Aggregation.sort(org.springframework.data.domain.Sort.Direction.ASC, "interestCount"),
                Aggregation.limit(1)
        );

        AggregationResults<ProfileInterestDTO> results = mongoTemplate.aggregate(
                aggregation, "interest_tracking", ProfileInterestDTO.class);
        ProfileInterestDTO result = results.getUniqueMappedResult();
        if (result != null) {
            logger.info("Least interested profile: {} with count: {}", result.getProfileId(), result.getInterestCount());
        }
        return result;
    }

    private FavouriteProfileDTO mapToFavouriteProfileDTOByProfileId(String profileId) {
        Profile profile = profileService.getProfileByProfileId(profileId).orElse(null);
        if (profile == null) {
            logger.warn("Profile not found for profileId: {}", profileId);
            return null;
        }
        return buildFavouriteProfileDTO(profile);
    }

    private FavouriteProfileDTO mapToFavouriteProfileDTOByUserId(String userId) {
        Profile profile = profileService.getProfileByUserId(userId).orElse(null);
        if (profile == null) {
            logger.warn("Profile not found for userId: {}", userId);
            return null;
        }
        return buildFavouriteProfileDTO(profile);
    }

    private FavouriteProfileDTO buildFavouriteProfileDTO(Profile profile) {
        ProfileInfo profileInfo = profileInfoService.getProfileInfoByProfileId(profile.getProfileId()).orElse(null);
        Optional<ProfileImages> profileImagesOpt = profileImagesService.getProfileImagesByProfileId(profile.getProfileId());

        FavouriteProfileDTO dto = new FavouriteProfileDTO();
        dto.setProfileId(profile.getProfileId());
        dto.setUserId(profile.getUserId());
        dto.setFirstName(profileInfo != null ? profileInfo.getFirstName() : "Unknown");
        dto.setAge(profileInfo != null && profileInfo.getDob() != null ? calculateAge(profileInfo.getDob()) : null);
        dto.setGender(profileInfo != null ? profileInfo.getGender() : null);
        dto.setDesignation(carrierService.getCarrierByProfileId(profile.getProfileId())
                .map(c -> c.getDesignation()).orElse(null));
        dto.setProfileImage(profileImagesOpt.map(ProfileImages::getPrimaryImage).orElse(null));
        return dto;
    }

    private Integer calculateAge(String dob) {
        if (dob == null) return null;
        if (!dob.matches("\\d{2}-\\d{2}-\\d{4}")) {
            logger.error("Invalid DOB format: {}", dob);
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
            LocalDate birthDate = LocalDate.parse(dob, formatter);
            return Period.between(birthDate, LocalDate.now()).getYears();
        } catch (Exception e) {
            logger.error("Error parsing dob: {}", dob, e);
            return null;
        }
    }
}