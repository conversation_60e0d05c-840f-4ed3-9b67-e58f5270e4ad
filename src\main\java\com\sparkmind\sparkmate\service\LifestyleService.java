package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.Lifestyle;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.repository.LifestyleRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LifestyleService {

    private static final Logger logger = LoggerFactory.getLogger(LifestyleService.class);

    @Autowired
    private LifestyleRepository lifestyleRepository;

    @Autowired
    private ProfileService profileService;

    public Lifestyle createLifestyle(String userId, Lifestyle lifestyle) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        lifestyle.setProfileId(profile.getProfileId());
        logger.info("Creating lifestyle for profileId: {}", profile.getProfileId());
        return lifestyleRepository.save(lifestyle);
    }

    public List<Lifestyle> getLifestyleByUserId(String userId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        logger.info("Fetching lifestyle for profileId: {}", profile.getProfileId());
        return lifestyleRepository.findByProfileId(profile.getProfileId());
    }

    public Lifestyle updateLifestyle(String userId, String lifestyleId, Lifestyle updatedLifestyle) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        Lifestyle lifestyle = lifestyleRepository.findById(lifestyleId)
                .filter(l -> l.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new RuntimeException("Lifestyle not found or not owned by user"));

        lifestyle.setPreferences(updatedLifestyle.getPreferences());
        logger.info("Updating lifestyle id: {} for profileId: {}", lifestyleId, profile.getProfileId());
        return lifestyleRepository.save(lifestyle);
    }

    public void deleteLifestyle(String userId, String lifestyleId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Profile not found for user"));
        Lifestyle lifestyle = lifestyleRepository.findById(lifestyleId)
                .filter(l -> l.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new RuntimeException("Lifestyle not found or not owned by user"));
        logger.info("Deleting lifestyle id: {} for profileId: {}", lifestyleId, profile.getProfileId());
        lifestyleRepository.delete(lifestyle);
    }

    public List<Lifestyle> getLifestyleByProfileIds(List<String> profileIds) {
        logger.info("Fetching lifestyles for profileIds: {}", profileIds);
        return lifestyleRepository.findByProfileIdIn(profileIds);
    }

    @Cacheable(value = "lifestyleTags", key = "#profileId")
    public Lifestyle getLifestyleTagsByProfileId(String profileId) {
        logger.info("Fetching lifestyle tags for profileId: {}", profileId);
        return lifestyleRepository.findByProfileId(profileId).stream().findFirst()
                .orElseThrow(() -> new RuntimeException("Lifestyle not found for profileId: " + profileId));
    }
}