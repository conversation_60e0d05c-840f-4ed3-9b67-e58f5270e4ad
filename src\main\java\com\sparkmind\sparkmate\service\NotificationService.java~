package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.Notification;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.User;
import com.sparkmind.sparkmate.repository.NotificationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NotificationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private ProfileService profileService;

    @CacheEvict(value = {"notifications", "recentNotifications"}, key = "#receiverProfileId.concat('.user')")
    public Notification createNotification(String senderUserId, String receiverProfileId) {
        try {
            User sender = userService.getUserById(senderUserId)
                    .orElseThrow(() -> new IllegalArgumentException("Sender user not found"));
            Profile receiverProfile = profileService.getProfileByProfileId(receiverProfileId)
                    .orElseThrow(() -> new IllegalArgumentException("Receiver profile not found"));

            Notification notification = new Notification();
            notification.setSenderUserId(senderUserId);
            notification.setReceiverUserId(receiverProfile.getUserId());
            notification.setReceiverProfileId(receiverProfileId);
            notification.setMessage(String.format("%s has liked your profile", sender.getName()));

            logger.debug("Creating notification: {} liked profile {}", sender.getName(), receiverProfileId);
            Notification savedNotification = notificationRepository.save(notification);
            logger.info("Created notification id: {}", savedNotification.getId());
            return savedNotification;
        } catch (Exception e) {
            logger.error("Error creating notification: {}", e.getMessage());
            throw new RuntimeException("Failed to create notification: " + e.getMessage());
        }
    }

    @CacheEvict(value = {"notifications", "recentNotifications"}, key = "#receiverUserId")
    public Notification createInterestNotification(String senderUserId, String receiverUserId) {
        try {
            User sender = userService.getUserById(senderUserId)
                    .orElseThrow(() -> new IllegalArgumentException("Sender user not found"));
            User receiver = userService.getUserById(receiverUserId)
                    .orElseThrow(() -> new IllegalArgumentException("Receiver user not found"));

            Notification notification = new Notification();
            notification.setSenderUserId(senderUserId);
            notification.setReceiverUserId(receiverUserId);
            notification.setReceiverProfileId(profileService.getProfileByUserId(receiverUserId)
                    .map(Profile::getProfileId).orElse(null));
            notification.setMessage(String.format("%s has shown interest in your profile", sender.getName()));

            logger.debug("Creating interest notification: {} interested in user {}", sender.getName(), receiverUserId);
            Notification savedNotification = notificationRepository.save(notification);
            logger.info("Created interest notification id: {}", savedNotification.getId());
            return savedNotification;
        } catch (Exception e) {
            logger.error("Error creating interest notification: {}", e.getMessage());
            throw new RuntimeException("Failed to create interest notification: " + e.getMessage());
        }
    }

    @Cacheable(value = "notifications", key = "#userId", unless = "#result.isEmpty()")
    public List<Notification> getAllNotifications(String userId) {
        logger.debug("Fetching all notifications for user: {}", userId);
        return notificationRepository.findByReceiverUserIdOrderByCreatedAtDesc(userId);
    }

    @Cacheable(value = "recentNotifications", key = "#userId", unless = "#result.isEmpty()")
    public List<Notification> getRecentNotifications(String userId) {
        logger.debug("Fetching recent notifications for user: {}", userId);
        return notificationRepository.findTop4ByReceiverUserIdOrderByCreatedAtDesc(userId);
    }
}