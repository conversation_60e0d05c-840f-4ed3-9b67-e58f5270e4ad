package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.ProfileEducationDetails;
import com.sparkmind.sparkmate.repository.ProfileEducationDetailsRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ProfileEducationDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(ProfileEducationDetailsService.class);

    @Autowired
    private ProfileEducationDetailsRepository repository;

    @Autowired
    private ProfileService profileService;

    public ProfileEducationDetails addEducation(String userId, ProfileEducationDetails educationDetails) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user"));
        educationDetails.setProfileId(profile.getProfileId());
        logger.info("Adding education details for profileId: {}", profile.getProfileId());
        return repository.save(educationDetails);
    }

    public Optional<ProfileEducationDetails> getEducation(String userId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user"));
        logger.info("Fetching education details for profileId: {}", profile.getProfileId());
        return repository.findFirstByProfileId(profile.getProfileId());
    }

    public List<ProfileEducationDetails> getAllEducationDetails() {
        logger.info("Fetching all education details");
        return repository.findAll();
    }

    public ProfileEducationDetails updateEducation(String userId, String educationId, ProfileEducationDetails updatedEducationDetails) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user"));
        ProfileEducationDetails educationDetails = repository.findById(educationId)
                .filter(ed -> ed.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new IllegalArgumentException("Education details not found or not owned by user"));
        educationDetails.setSchool(updatedEducationDetails.getSchool());
        educationDetails.setGraduation(updatedEducationDetails.getGraduation());
        educationDetails.setPostGraduation(updatedEducationDetails.getPostGraduation());
        educationDetails.setUniversity(updatedEducationDetails.getUniversity());
        logger.info("Updating education details id: {} for profileId: {}", educationId, profile.getProfileId());
        return repository.save(educationDetails);
    }

    public void deleteEducation(String userId, String profileId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user"));
        if (!repository.existsByProfileId(profileId) || !profile.getProfileId().equals(profileId)) {
            throw new IllegalArgumentException("Education details not found or not owned by user");
        }
        logger.info("Deleting education details for profileId: {}", profileId);
        repository.deleteByProfileId(profileId);
    }
}