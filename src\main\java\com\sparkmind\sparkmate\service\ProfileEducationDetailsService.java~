package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.ProfileEducationDetails;
import com.sparkmind.sparkmate.repository.ProfileEducationDetailsRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ProfileEducationDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(ProfileEducationDetailsService.class);

    @Autowired
    private ProfileEducationDetailsRepository repository;

    @Autowired
    private ProfileService profileService;

    @CacheEvict(value = {"educationByUserId", "allEducationDetails"}, allEntries = true)
    public ProfileEducationDetails addEducation(String userId, ProfileEducationDetails educationDetails) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user"));
        educationDetails.setProfileId(profile.getProfileId());
        logger.debug("Adding education details for profileId: {}", profile.getProfileId());
        ProfileEducationDetails savedEducation = repository.save(educationDetails);
        logger.info("Added education details with id: {}", savedEducation.getId());
        return savedEducation;
    }

    @Cacheable(value = "educationByUserId", key = "#userId", unless = "#result == null || !#result.isPresent()")
    public Optional<ProfileEducationDetails> getEducation(String userId) {
        logger.debug("Fetching education details for userId: {}", userId);
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user"));
        return repository.findFirstByProfileId(profile.getProfileId());
    }

    @Cacheable(value = "allEducationDetails", unless = "#result.isEmpty()")
    public List<ProfileEducationDetails> getAllEducationDetails() {
        logger.debug("Fetching all education details");
        return repository.findAll();
    }

    @CacheEvict(value = {"educationByUserId", "allEducationDetails"}, allEntries = true)
    public ProfileEducationDetails updateEducation(String userId, String educationId, ProfileEducationDetails updatedEducationDetails) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user"));
        ProfileEducationDetails educationDetails = repository.findById(educationId)
                .filter(ed -> ed.getProfileId().equals(profile.getProfileId()))
                .orElseThrow(() -> new IllegalArgumentException("Education details not found or not owned by user"));
        educationDetails.setSchool(updatedEducationDetails.getSchool());
        educationDetails.setGraduation(updatedEducationDetails.getGraduation());
        educationDetails.setPostGraduation(updatedEducationDetails.getPostGraduation());
        educationDetails.setUniversity(updatedEducationDetails.getUniversity());
        logger.debug("Updating education details id: {} for profileId: {}", educationId, profile.getProfileId());
        ProfileEducationDetails updatedEducation = repository.save(educationDetails);
        logger.info("Updated education details id: {}", educationId);
        return updatedEducation;
    }

    @CacheEvict(value = {"educationByUserId", "allEducationDetails"}, allEntries = true)
    public void deleteEducation(String userId, String profileId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user"));
        if (!repository.existsByProfileId(profileId) || !profile.getProfileId().equals(profileId)) {
            throw new IllegalArgumentException("Education details not found or not owned by user");
        }
        logger.debug("Deleting education details for profileId: {}", profileId);
        repository.deleteByProfileId(profileId);
        logger.info("Deleted education details for profileId: {}", profileId);
    }
}