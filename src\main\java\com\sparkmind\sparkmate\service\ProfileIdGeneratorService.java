package com.sparkmind.sparkmate.service;

import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class ProfileIdGeneratorService {
    private static final String ALPHABET = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String DIGITS = "0123456789";
    private final SecureRandom random = new SecureRandom();

    public synchronized String generateProfileId() {
        // Minimum length of 15 characters: 5 digits + 10 letters
        int totalLength = 15;
        int digitCount = 5;
        int letterCount = totalLength - digitCount;

        // Create lists for letters and digits
        List<Character> idChars = new ArrayList<>(totalLength);

        // Add 5 random digits
        for (int i = 0; i < digitCount; i++) {
            idChars.add(DIGITS.charAt(random.nextInt(DIGITS.length())));
        }

        // Add 10 random letters
        for (int i = 0; i < letterCount; i++) {
            idChars.add(ALPHABET.charAt(random.nextInt(ALPHABET.length())));
        }

        // Shuffle to intermix letters and digits
        Collections.shuffle(idChars, random);

        // Build the final string
        StringBuilder profileId = new StringBuilder(totalLength);
        for (Character c : idChars) {
            profileId.append(c);
        }

        return profileId.toString();
    }
}