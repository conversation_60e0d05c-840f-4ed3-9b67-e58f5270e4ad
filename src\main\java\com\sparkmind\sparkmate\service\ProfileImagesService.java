package com.sparkmind.sparkmate.service;

import com.google.cloud.storage.*;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.ProfileImages;
import com.sparkmind.sparkmate.repository.ProfileImagesRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
public class ProfileImagesService {

    private static final Logger logger = LoggerFactory.getLogger(ProfileImagesService.class);
    private final String BUCKET_NAME;

    private final ProfileImagesRepository repository;
    private final ProfileService profileService;
    private final Storage storage;

    @Autowired
    public ProfileImagesService(
            ProfileImagesRepository repository,
            ProfileService profileService,
            Storage storage,
            @Value("${gcs.bucket.name:sparkmate-new-storage}") String bucketName) {
        this.repository = repository;
        this.profileService = profileService;
        this.storage = storage;
        this.BUCKET_NAME = bucketName;
        logger.info("Initialized ProfileImagesService with bucket: {}", BUCKET_NAME);
    }

    public void upsertImages(String profileId, List<String> imageUrls, String primaryImageUrl) {
        profileService.getProfileByProfileId(profileId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for profileId: " + profileId));
        Optional<ProfileImages> existingImages = repository.findByProfileId(profileId);
        ProfileImages images = existingImages.orElse(new ProfileImages());
        images.setProfileId(profileId);
        if (imageUrls != null && !imageUrls.isEmpty()) {
            images.setImages(imageUrls);
            if (primaryImageUrl != null) {
                if (imageUrls.contains(primaryImageUrl)) {
                    images.setPrimaryImage(primaryImageUrl);
                } else {
                    throw new IllegalArgumentException("Primary image URL must be in the provided image URLs list");
                }
            }
        } else {
            images.setImages(new ArrayList<>());
            images.setPrimaryImage(null);
        }
        repository.save(images);
        logger.info("Upserted images for profileId: {}", profileId);
    }

    public Optional<ProfileImages> retrieveProfileImagesOptional(String profileId) {
        return repository.findByProfileId(profileId);
    }

    public ProfileImages uploadOrUpdateProfileImages(String userId, MultipartFile[] files) throws IOException {
        logger.info("Starting image upload for userId: {}", userId);

        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user: " + userId));

        if (files == null || files.length == 0) {
            throw new IllegalArgumentException("No files uploaded");
        }

        Optional<ProfileImages> existingImages = repository.findByProfileId(profile.getProfileId());
        ProfileImages profileImages = existingImages.orElse(new ProfileImages());
        profileImages.setProfileId(profile.getProfileId());

        List<String> imageUrls = new ArrayList<>();
        for (MultipartFile file : files) {
            if (file.isEmpty() || file.getBytes().length == 0) {
                throw new IllegalArgumentException("One or more uploaded files are empty");
            }

            String fileName = "profiles/" + profile.getProfileId() + "/" + UUID.randomUUID() + "-" + file.getOriginalFilename();
            BlobId blobId = BlobId.of(BUCKET_NAME, fileName);
            BlobInfo blobInfo = BlobInfo.newBuilder(blobId)
                    .setContentType(file.getContentType())
                    .build();

            try {
                logger.debug("Uploading file {} to bucket {}", fileName, BUCKET_NAME);
                Blob blob = storage.create(blobInfo, file.getBytes());

                // Make it public
                storage.update(
                        blob.toBuilder().setAcl(
                                List.of(Acl.of(Acl.User.ofAllUsers(), Acl.Role.READER))
                        ).build()
                );

                // Construct public URL
                String publicUrl = String.format("https://storage.googleapis.com/%s/%s", BUCKET_NAME, fileName);
                imageUrls.add(publicUrl);

                logger.debug("Uploaded file {} successfully, public URL: {}", fileName, publicUrl);
            } catch (StorageException e) {
                logger.error("Failed to upload file {} to GCS: {}", fileName, e.getMessage());
                throw new IOException("Failed to upload image to GCS: " + e.getMessage(), e);
            }

        }

        if (existingImages.isPresent()) {
            profileImages.getImages().addAll(imageUrls);
        } else {
            profileImages.setImages(imageUrls);
            if (!imageUrls.isEmpty() && profileImages.getPrimaryImage() == null) {
                profileImages.setPrimaryImage(imageUrls.get(0));
            }
        }

        logger.info("Uploading/updating images for profileId: {}", profile.getProfileId());
        return repository.save(profileImages);
    }

    public ProfileImages retrieveProfileImages(String profileId) {
        profileService.getProfileByProfileId(profileId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for profileId: " + profileId));
        logger.info("Retrieving images for profileId: {}", profileId);
        return repository.findByProfileId(profileId).orElse(null);
    }


    public Optional<ProfileImages> getProfileImagesByProfileId(String profileId) {
        return repository.findByProfileId(profileId);
    }

    public void removeProfileImages(String userId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user: " + userId));
        Optional<ProfileImages> images = repository.findByProfileId(profile.getProfileId());
        if (images.isPresent()) {
            ProfileImages profileImages = images.get();
            for (String imageUrl : profileImages.getImages()) {
                String fileName = extractFileNameFromUrl(imageUrl);
                try {
                    storage.delete(BlobId.of(BUCKET_NAME, fileName));
                    logger.debug("Deleted file {} from GCS", fileName);
                } catch (StorageException e) {
                    logger.warn("Failed to delete file {} from GCS: {}", fileName, e.getMessage());
                }
            }
            logger.info("Removing all images for profileId: {}", profile.getProfileId());
            repository.delete(profileImages);
        } else {
            throw new IllegalArgumentException("No images found for profile: " + profile.getProfileId());
        }
    }

    public ProfileImages setPrimaryProfileImage(String userId, int imageIndex) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user: " + userId));

        Optional<ProfileImages> profileImagesOpt = repository.findByProfileId(profile.getProfileId());
        ProfileImages profileImages = profileImagesOpt.orElseThrow(() ->
                new IllegalArgumentException("No images found for profile: " + profile.getProfileId()));

        if (imageIndex < 0 || imageIndex >= profileImages.getImages().size()) {
            throw new IllegalArgumentException("Invalid image index: " + imageIndex);
        }

        profileImages.setPrimaryImage(profileImages.getImages().get(imageIndex));
        logger.info("Setting primary image for profileId: {} at index: {}", profile.getProfileId(), imageIndex);
        return repository.save(profileImages);
    }

    public ProfileImages makeImagePublic(String userId, int imageIndex) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found for user: " + userId));

        Optional<ProfileImages> profileImagesOpt = repository.findByProfileId(profile.getProfileId());
        ProfileImages profileImages = profileImagesOpt.orElseThrow(() ->
                new IllegalArgumentException("No images found for profile: " + profile.getProfileId()));

        if (imageIndex < 0 || imageIndex >= profileImages.getImages().size()) {
            throw new IllegalArgumentException("Invalid image index: " + imageIndex);
        }

        String imageUrl = profileImages.getImages().get(imageIndex);
        String fileName = extractFileNameFromUrl(imageUrl);
        BlobId blobId = BlobId.of(BUCKET_NAME, fileName);
        Blob blob = storage.get(blobId);

        if (blob == null) {
            throw new IllegalStateException("Image not found in storage: " + fileName);
        }

        try {
            storage.update(
                    BlobInfo.newBuilder(blobId).build(),
                    Storage.BlobTargetOption.predefinedAcl(Storage.PredefinedAcl.PUBLIC_READ)
            );
            String publicUrl = String.format("https://storage.googleapis.com/%s/%s", BUCKET_NAME, fileName);
            profileImages.getImages().set(imageIndex, publicUrl);
            if (profileImages.getPrimaryImage().equals(imageUrl)) {
                profileImages.setPrimaryImage(publicUrl);
            }
            logger.info("Made image public for profileId: {} at index: {}", profile.getProfileId(), imageIndex);
            return repository.save(profileImages);
        } catch (StorageException e) {
            logger.error("Failed to make image public: {}", e.getMessage());
            throw new RuntimeException("Failed to make image public: " + e.getMessage(), e);
        }
    }

    private String extractFileNameFromUrl(String url) {
        if (url.startsWith("https://storage.googleapis.com/" + BUCKET_NAME + "/")) {
            return url.replace("https://storage.googleapis.com/" + BUCKET_NAME + "/", "");
        } else {
            return url.substring(url.indexOf("profiles/"));
        }
    }
}