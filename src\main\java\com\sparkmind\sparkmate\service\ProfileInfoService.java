package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.ProfileInfo;
import com.sparkmind.sparkmate.repository.ProfileInfoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ProfileInfoService {
    @Autowired
    private ProfileInfoRepository profileInfoRepository;

    @Autowired
    private ProfileService profileService;

    public ProfileInfo createProfileInfo(String profileId, ProfileInfo profileInfo) {
        profileInfo.setProfileId(profileId);
        return profileInfoRepository.save(profileInfo);
    }

    public Optional<ProfileInfo> getProfileInfoByUserId(String userId) {
        Profile profile = profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));
        return profileInfoRepository.findByProfileId(profile.getProfileId());
    }

    @Cacheable(value = "profileInfo", key = "#profileId")
    public Optional<ProfileInfo> getProfileInfoByProfileId(String profileId) {
        return profileInfoRepository.findByProfileId(profileId);
    }

    @CacheEvict(value = "profileInfo", key = "#id")
    public ProfileInfo updateProfileInfo(String id, ProfileInfo profileInfo) {
        ProfileInfo existing = profileInfoRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("ProfileInfo not found"));
        existing.setFirstName(profileInfo.getFirstName());
        existing.setLastName(profileInfo.getLastName());
        existing.setGender(profileInfo.getGender());
        existing.setDob(profileInfo.getDob());
        existing.setMaritalStatus(profileInfo.getMaritalStatus());
        existing.setReligion(profileInfo.getReligion());
        existing.setCaste(profileInfo.getCaste());
        existing.setMangal(profileInfo.getMangal());
        existing.setZodiacSign(profileInfo.getZodiacSign());
        existing.setCountry(profileInfo.getCountry());
        existing.setState(profileInfo.getState());
        existing.setCity(profileInfo.getCity());
        existing.setAboutMe(profileInfo.getAboutMe());
        existing.setLookingFor(profileInfo.getLookingFor());
        return profileInfoRepository.save(existing);
    }
}