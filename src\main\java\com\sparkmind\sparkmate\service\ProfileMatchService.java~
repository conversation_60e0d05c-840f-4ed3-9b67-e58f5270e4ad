package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.dto.ProfileMatchDTO;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.ProfileInfo;
import com.sparkmind.sparkmate.model.ProfileMatch;
import com.sparkmind.sparkmate.repository.ProfileInfoRepository;
import com.sparkmind.sparkmate.repository.ProfileMatchRepository;
import com.sparkmind.sparkmate.repository.ProfileRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ProfileMatchService {

    private static final Logger logger = LoggerFactory.getLogger(ProfileMatchService.class);

    @Autowired
    private ProfileMatchRepository profileMatchRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ProfileInfoRepository profileInfoRepository;

    public List<ProfileMatchDTO> getMatchingProfiles(String profileId) {
        // Find the profile by ID
        Profile profile = profileRepository.findByProfileId(profileId).orElse(null);
        if (profile == null) {
            logger.warn("Profile not found for profileId: {}", profileId);
            return List.of();
        }

        // Get profile info
        ProfileInfo profileInfo = profileInfoRepository.findByProfileId(profileId).orElse(null);
        if (profileInfo == null) {
            logger.warn("ProfileInfo not found for profileId: {}", profileId);
            return List.of();
        }

        // Validate required fields
        if (!isValidProfileInfo(profileInfo)) {
            logger.warn("Invalid ProfileInfo for profileId: {}. Missing required fields.", profileId);
            return List.of();
        }

        // Find matching profiles
        List<ProfileInfo> matchedProfileInfos = profileInfoRepository.findAll().stream()
                .filter(pi -> !pi.getProfileId().equals(profileId)) // Exclude self
                .filter(pi -> isValidProfileInfo(pi)) // Ensure matched profile has required fields
                .filter(pi -> !pi.getGender().equalsIgnoreCase(profileInfo.getGender())) // Opposite gender
                .filter(pi -> pi.getEducation().equalsIgnoreCase(profileInfo.getEducation())) // Same education
                .filter(pi -> pi.getCaste().equalsIgnoreCase(profileInfo.getCaste())) // Same caste
                .filter(pi -> pi.getReligion().equalsIgnoreCase(profileInfo.getReligion())) // Same religion
                .filter(pi -> isAgeMatch(profileInfo, pi)) // Custom age logic
                .collect(Collectors.toList());

        // Convert to DTOs
        List<ProfileMatchDTO> profileMatchDTOs = matchedProfileInfos.stream()
                .map(pi -> new ProfileMatchDTO(profileId, pi.getProfileId()))
                .collect(Collectors.toList());

        // Save new matches if they don’t exist
        List<ProfileMatch> newMatches = matchedProfileInfos.stream()
                .filter(pi -> profileMatchRepository.findByProfileIdAndMatchedProfileId(profileId, pi.getProfileId()).isEmpty())
                .map(pi -> {
                    ProfileMatch profileMatch = new ProfileMatch();
                    profileMatch.setProfileId(profileId);
                    profileMatch.setMatchedProfileId(pi.getProfileId());
                    return profileMatch;
                })
                .collect(Collectors.toList());

        if (!newMatches.isEmpty()) {
            logger.info("Saving {} new matches for profileId: {}", newMatches.size(), profileId);
            profileMatchRepository.saveAll(newMatches);
        }

        return profileMatchDTOs;
    }

    // Validate required fields in ProfileInfo
    private boolean isValidProfileInfo(ProfileInfo profileInfo) {
        return profileInfo.getGender() != null && !profileInfo.getGender().isBlank() &&
                profileInfo.getAge() != null &&
                profileInfo.getEducation() != null && !profileInfo.getEducation().isBlank() &&
                profileInfo.getCaste() != null && !profileInfo.getCaste().isBlank() &&
                profileInfo.getReligion() != null && !profileInfo.getReligion().isBlank();
    }

    // Custom age matching logic based on gender
    private boolean isAgeMatch(ProfileInfo profileInfo, ProfileInfo matchedProfileInfo) {
        int userAge = profileInfo.getAge();
        int matchAge = matchedProfileInfo.getAge();

        if (profileInfo.getGender().equalsIgnoreCase("female")) {
            // Female: Match males equal or up to 5 years older
            return matchAge >= userAge && matchAge <= userAge + 5;
        } else if (profileInfo.getGender().equalsIgnoreCase("male")) {
            // Male: Match females equal or up to 5 years younger
            return matchAge <= userAge && matchAge >= userAge - 5;
        }
        return false; // Invalid gender case
    }
}