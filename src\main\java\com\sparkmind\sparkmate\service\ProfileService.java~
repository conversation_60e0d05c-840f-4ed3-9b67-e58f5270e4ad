package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.repository.ProfileRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ProfileService {

    private static final Logger logger = LoggerFactory.getLogger(ProfileService.class);

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ProfileIdGeneratorService profileIdGenerator;

    @Transactional
    public Profile createProfile(String userId) {
        Optional<Profile> existingProfile = profileRepository.findByUserId(userId);
        if (existingProfile.isPresent()) {
            logger.info("Profile already exists for userId: {}, skipping creation", userId);
            return existingProfile.get();
        }
        Profile profile = new Profile();
        profile.setUserId(userId);
        profile.setProfileId(profileIdGenerator.generateProfileId());
        profile.setCreatedOn(LocalDateTime.now());
        profile.setUpdatedOn(LocalDateTime.now());
        profile.setActive(true);
        logger.info("Created profile with profileId: {} for userId: {}", profile.getProfileId(), userId);
        return profileRepository.save(profile);
    }

    @Cacheable(value = "profilesByUserId", key = "#userId")
    public Optional<Profile> getProfileByUserId(String userId) {
        logger.info("Fetching profile for userId: {}", userId);
        return profileRepository.findByUserId(userId);
    }

    public Map<String, Profile> getProfilesByUserIds(List<String> userIds) {
        return profileRepository.findByUserIdIn(userIds)
                .stream().collect(Collectors.toMap(Profile::getUserId, p -> p));
    }

    @Cacheable(value = "profilesByProfileId", key = "#profileId")
    public Optional<Profile> getProfileByProfileId(String profileId) {
        logger.info("Fetching profile with profileId: {}", profileId);
        return profileRepository.findByProfileId(profileId);
    }

    public List<Profile> getAllProfiles() {
        logger.info("Fetching all profiles");
        return profileRepository.findAll();
    }

    @CacheEvict(value = {"profilesByUserId", "profilesByProfileId"}, key = "#profileId")
    public Profile updateProfile(String profileId, Profile profile) {
        Profile existing = profileRepository.findByProfileId(profileId)
                .orElseThrow(() -> {
                    logger.warn("Profile not found with profileId: {}", profileId);
                    return new RuntimeException("Profile not found");
                });
        profile.setProfileId(profileId);
        profile.setUpdatedOn(LocalDateTime.now());
        logger.info("Updated profile with profileId: {}", profileId);
        return profileRepository.save(profile);
    }

    public List<Profile> getAllActiveProfiles() {
        logger.info("Fetching all active profiles");
        return profileRepository.findByActive(true);
    }
}