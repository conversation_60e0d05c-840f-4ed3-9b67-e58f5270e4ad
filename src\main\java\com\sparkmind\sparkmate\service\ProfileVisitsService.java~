package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.dto.ProfileVisitCardDTO;
import com.sparkmind.sparkmate.model.Profile;
import com.sparkmind.sparkmate.model.ProfileImages;
import com.sparkmind.sparkmate.model.ProfileInfo;
import com.sparkmind.sparkmate.model.ProfileVisits;
import com.sparkmind.sparkmate.repository.ProfileVisitsRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ProfileVisitsService {

    private static final Logger logger = LoggerFactory.getLogger(ProfileVisitsService.class);

    @Autowired
    private ProfileVisitsRepository profileVisitsRepository;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private ProfileInfoService profileInfoService;

    @Autowired
    private CarrierService carrierService;

    @Autowired
    private ProfileImagesService profileImagesService;

    @CacheEvict(value = {"profileVisitsByProfileId", "mostRecentVisitByProfileId", "profilesVisitedByUserId"}, allEntries = true)
    public ProfileVisits recordProfileVisit(String visitToProfile, String visitFromUserId) {
        profileService.getProfileByProfileId(visitToProfile)
                .orElseThrow(() -> new IllegalArgumentException("Profile to visit not found"));
        profileService.getProfileByUserId(visitFromUserId)
                .orElseThrow(() -> new IllegalArgumentException("Visiting user profile not found"));

        logger.debug("Recording profile visit from userId {} to profileId {}", visitFromUserId, visitToProfile);
        Optional<ProfileVisits> optionalProfileVisit = profileVisitsRepository.findByVisitToProfileAndVisitFromUserId(visitToProfile, visitFromUserId);

        ProfileVisits profileVisit;
        try {
            if (optionalProfileVisit.isEmpty()) {
                profileVisit = new ProfileVisits();
                profileVisit.setVisitToProfile(visitToProfile);
                profileVisit.setVisitFromUserId(visitFromUserId);
                profileVisit.setVisitCount(1);
                profileVisit.setLastVisitedOn(LocalDateTime.now());
            } else {
                profileVisit = optionalProfileVisit.get();
                profileVisit.setVisitCount(profileVisit.getVisitCount() + 1);
                profileVisit.setLastVisitedOn(LocalDateTime.now());
            }
            ProfileVisits savedVisit = profileVisitsRepository.save(profileVisit);
            logger.info("Recorded visit from userId {} to profileId {}, count: {}", visitFromUserId, visitToProfile, savedVisit.getVisitCount());
            return savedVisit;
        } catch (DuplicateKeyException e) {
            logger.warn("Duplicate visit detected, updating existing record for userId {} to profileId {}", visitFromUserId, visitToProfile);
            profileVisit = profileVisitsRepository.findByVisitToProfileAndVisitFromUserId(visitToProfile, visitFromUserId).get();
            profileVisit.setVisitCount(profileVisit.getVisitCount() + 1);
            profileVisit.setLastVisitedOn(LocalDateTime.now());
            return profileVisitsRepository.save(profileVisit);
        }
    }

    @Cacheable(value = "profileVisitsByProfileId", key = "#profileId", unless = "#result.isEmpty()")
    public List<ProfileVisitCardDTO> getAllProfileVisits(String profileId) {
        profileService.getProfileByProfileId(profileId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));

        logger.debug("Fetching all visits for profileId: {}", profileId);
        List<ProfileVisits> visits = profileVisitsRepository.findByVisitToProfile(profileId);
        return visits.stream()
                .map(this::mapToProfileVisitCardDTO)
                .filter(dto -> dto != null)
                .collect(Collectors.toList());
    }

    @Cacheable(value = "mostRecentVisitByProfileId", key = "#profileId", unless = "#result == null")
    public ProfileVisitCardDTO getMostRecentVisit(String profileId) {
        profileService.getProfileByProfileId(profileId)
                .orElseThrow(() -> new IllegalArgumentException("Profile not found"));
        logger.debug("Fetching most recent visit for profileId: {}", profileId);

        ProfileVisits profileVisits = profileVisitsRepository.findTopByVisitToProfileOrderByLastVisitedOnDesc(profileId)
                .orElse(null);
        return profileVisits != null ? mapToProfileVisitCardDTO(profileVisits) : null;
    }

    @Cacheable(value = "profilesVisitedByUserId", key = "#userId", unless = "#result.isEmpty()")
    public List<ProfileVisitCardDTO> getProfilesVisitedByUser(String userId, List<String> activeProfileIds) {
        profileService.getProfileByUserId(userId)
                .orElseThrow(() -> new IllegalArgumentException("User profile not found"));

        logger.debug("Fetching profiles visited by userId: {}", userId);
        List<ProfileVisits> visits = profileVisitsRepository.findAllByVisitFromUserIdAndVisitToProfileIn(userId, activeProfileIds);
        return visits.stream()
                .map(visit -> {
                    Profile profile = profileService.getProfileByProfileId(visit.getVisitToProfile()).orElse(null);
                    return profile != null ? mapToProfileVisitCardDTOForVisited(profile, visit) : null;
                })
                .filter(dto -> dto != null)
                .collect(Collectors.toList());
    }

    private ProfileVisitCardDTO mapToProfileVisitCardDTO(ProfileVisits visit) {
        Profile profile = profileService.getProfileByUserId(visit.getVisitFromUserId()).orElse(null);
        if (profile == null) {
            logger.warn("No profile found for visitFromUserId: {}", visit.getVisitFromUserId());
            return null;
        }
        ProfileInfo profileInfo = profileInfoService.getProfileInfoByProfileId(profile.getProfileId()).orElse(null);
        Optional<ProfileImages> profileImagesOpt = profileImagesService.getProfileImagesByProfileId(profile.getProfileId());

        ProfileVisitCardDTO dto = new ProfileVisitCardDTO();
        dto.setProfileId(profile.getProfileId());
        dto.setUserId(profile.getUserId());
        dto.setFirstName(profileInfo != null ? profileInfo.getFirstName() : "Unknown");
        dto.setAge(profileInfo != null && profileInfo.getDob() != null ? calculateAge(profileInfo.getDob()) : null);
        dto.setGender(profileInfo != null ? profileInfo.getGender() : null);
        dto.setDesignation(carrierService.getCarrierByProfileId(profile.getProfileId())
                .map(c -> c.getDesignation()).orElse(null));
        dto.setProfileImage(profileImagesOpt.map(ProfileImages::getPrimaryImage).orElse(null));
        return dto;
    }

    private ProfileVisitCardDTO mapToProfileVisitCardDTOForVisited(Profile profile, ProfileVisits visit) {
        ProfileInfo profileInfo = profileInfoService.getProfileInfoByProfileId(profile.getProfileId()).orElse(null);
        Optional<ProfileImages> profileImagesOpt = profileImagesService.getProfileImagesByProfileId(profile.getProfileId());

        ProfileVisitCardDTO dto = new ProfileVisitCardDTO();
        dto.setProfileId(profile.getProfileId());
        dto.setUserId(profile.getUserId());
        dto.setFirstName(profileInfo != null ? profileInfo.getFirstName() : "Unknown");
        dto.setAge(profileInfo != null && profileInfo.getDob() != null ? calculateAge(profileInfo.getDob()) : null);
        dto.setGender(profileInfo != null ? profileInfo.getGender() : null);
        dto.setDesignation(carrierService.getCarrierByProfileId(profile.getProfileId())
                .map(c -> c.getDesignation()).orElse(null));
        dto.setProfileImage(profileImagesOpt.map(ProfileImages::getPrimaryImage).orElse(null));
        return dto;
    }

    private Integer calculateAge(String dob) {
        if (dob == null) return null;
        try {
            LocalDate birthDate = LocalDate.parse(dob);
            return Period.between(birthDate, LocalDate.now()).getYears();
        } catch (Exception e) {
            logger.error("Error parsing dob: {}", dob, e);
            return null;
        }
    }
}