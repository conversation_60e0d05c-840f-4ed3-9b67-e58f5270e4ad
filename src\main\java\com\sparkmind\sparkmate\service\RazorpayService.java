package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.SubscriptionPlan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class RazorpayService {
    private static final Logger logger = LoggerFactory.getLogger(RazorpayService.class);

    // Placeholder methods for future Razorpay integration
    public String createCustomer(String userId) {
        logger.warn("Razorpay integration not implemented yet. Returning mock customer ID for user {}", userId);
        return "mock_customer_" + userId; // Mock for testing
    }

    public String createRazorpayPlan(SubscriptionPlan plan) {
        logger.warn("Razorpay integration not implemented yet. Returning mock plan ID for plan {}", plan.getPlanName());
        return "mock_plan_" + plan.getPlanId(); // Mock for testing
    }

    public String createRazorpaySubscription(String customerId, String planId, int totalCount) {
        logger.warn("Razorpay integration not implemented yet. Returning mock subscription ID");
        return "mock_sub_" + customerId + "_" + planId; // Mock for testing
    }
}