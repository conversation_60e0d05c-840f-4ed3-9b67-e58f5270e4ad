package com.sparkmind.sparkmate.service;

import com.razorpay.*;
import com.sparkmind.sparkmate.model.SubscriptionPlan;
import com.sparkmind.sparkmate.model.User;
import com.sparkmind.sparkmate.repository.UserRepository;
import jakarta.annotation.PostConstruct;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Optional;

@Service
public class RazorpayService {
    private static final Logger logger = LoggerFactory.getLogger(RazorpayService.class);

    @Value("rzp_test_LfPe7hfNWVCzoQ")
    private String keyId;

    @Value("sldfjljf")
    private String keySecret;

    @Autowired
    private UserRepository userRepository;

    private RazorpayClient razorpayClient;

    @PostConstruct
    public void init() throws RazorpayException {
        try {
            this.razorpayClient = new RazorpayClient(keyId, keySecret);
            logger.info("Initialized RazorpayClient with keyId: {}", keyId);
        } catch (RazorpayException e) {
            logger.error("Failed to initialize RazorpayClient: {}", e.getMessage(), e);
            throw e;
        }
    }

    public String getRazorpayKeyId() {
        return keyId;
    }

    public String createCustomer(String userId) throws RazorpayException {
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            logger.error("User {} not found", userId);
            throw new IllegalArgumentException("User not found");
        }
        User user = userOpt.get();

        if (user.getRazorpayCustomerId() != null) {
            logger.info("Customer already exists for user {}: {}", userId, user.getRazorpayCustomerId());
            return user.getRazorpayCustomerId();
        }

        try {
            JSONObject customerRequest = new JSONObject();
            customerRequest.put("name", user.getName());
            customerRequest.put("email", user.getEmail());
            customerRequest.put("contact", user.getMobile());

            Customer customer = razorpayClient.customers.create(customerRequest);
            String customerId = customer.get("id");
            user.setRazorpayCustomerId(customerId);
            userRepository.save(user);
            logger.info("Created Razorpay customer {} for user {}", customerId, userId);
            return customerId;
        } catch (RazorpayException e) {
            logger.error("Failed to create Razorpay customer for user {}: {}", userId, e.getMessage(), e);
            throw e;
        }
    }

    public String createRazorpayPlan(SubscriptionPlan plan) throws RazorpayException {
        try {
            JSONObject planRequest = new JSONObject();
            planRequest.put("period", plan.getPeriod().toLowerCase());
            planRequest.put("interval", plan.getInterval());
            planRequest.put("item", new JSONObject()
                    .put("name", plan.getPlanName())
                    .put("amount", (int) (plan.getMonthlyAmount() * 100)) // Convert to paise
                    .put("currency", "INR")
                    .put("description", plan.getDescription())
            );

            Plan razorpayPlan = razorpayClient.plans.create(planRequest);
            String planId = razorpayPlan.get("id");
            logger.info("Created Razorpay plan {} for plan {}", planId, plan.getPlanName());
            return planId;
        } catch (RazorpayException e) {
            logger.error("Failed to create Razorpay plan for {}: {}", plan.getPlanName(), e.getMessage(), e);
            throw e;
        }
    }

    public String createOrder(String userId, String planId, double amount) throws RazorpayException {
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            logger.error("User {} not found", userId);
            throw new IllegalArgumentException("User not found");
        }

        try {
            JSONObject orderRequest = new JSONObject();
            orderRequest.put("amount", (int) (amount * 100)); // Convert to paise
            orderRequest.put("currency", "INR");
            orderRequest.put("receipt", "receipt_" + userId + "_" + System.currentTimeMillis());
            orderRequest.put("payment_capture", 1);

            Order order = razorpayClient.orders.create(orderRequest);
            String orderId = order.get("id");
            logger.info("Created Razorpay order {} for user {} and plan {}", orderId, userId, planId);
            return orderId;
        } catch (RazorpayException e) {
            logger.error("Failed to create Razorpay order for user {} and plan {}: {}", userId, planId, e.getMessage(), e);
            throw e;
        }
    }

    public boolean verifyPayment(String orderId, String paymentId, String signature, String subscriptionId) throws RazorpayException {
        try {
            String payload = orderId + "|" + paymentId;
            String generatedSignature = generateHmacSha256(payload, keySecret);
            boolean isValid = generatedSignature.equals(signature);
            if (isValid) {
                logger.info("Payment signature verified for order {}, payment {}, subscription {}", orderId, paymentId, subscriptionId);
            } else {
                logger.error("Payment signature verification failed for order {}, payment {}, subscription {}", orderId, paymentId, subscriptionId);
            }
            return isValid;
        } catch (Exception e) {
            logger.error("Error verifying payment signature for subscription {}: {}", subscriptionId, e.getMessage(), e);
            throw new RazorpayException("Signature verification failed: " + e.getMessage());
        }
    }

    private String generateHmacSha256(String data, String secret) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] hmac = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        StringBuilder hexString = new StringBuilder();
        for (byte b : hmac) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }
}