package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.SubscriptionConfig;
import com.sparkmind.sparkmate.model.Subscriptions;
import com.sparkmind.sparkmate.model.UserActivity;
import com.sparkmind.sparkmate.repository.SubscriptionConfigRepository;
import com.sparkmind.sparkmate.repository.UserActivityRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;

@Service
public class SubscriptionManagerService {

    private static final Logger logger = LoggerFactory.getLogger(SubscriptionManagerService.class);

    @Autowired
    private UserActivityRepository userActivityRepository;

    @Autowired
    private SubscriptionConfigRepository subscriptionConfigRepository;

    @Autowired
    private SubscriptionService subscriptionService;

    @Autowired
    private SubscriptionPlanService subscriptionPlanService;

    public void initializeUserActivity(String userId, String subscriptionId) {
        UserActivity activity = new UserActivity();
        activity.setUserId(userId);
        activity.setSubscriptionId(subscriptionId);
        activity.setProfileVisitCount(0);
        activity.setVisitedProfileIds(new HashSet<>());
        activity.setMessageCount(0);
        activity.setMessagedUserIds(new HashSet<>());
        activity.setCreatedAt(LocalDateTime.now());
        activity.setUpdatedAt(LocalDateTime.now());
        userActivityRepository.save(activity);
        logger.info("Initialized UserActivity for user {} and subscription {}", userId, subscriptionId);
    }

    public boolean trackProfileVisit(String userId, String profileId) {
        Subscriptions sub = subscriptionService.getCurrentSubscription(userId);
        if (sub == null || !"active".equalsIgnoreCase(sub.getStatus())) {
            logger.warn("No active subscription for user {}", userId);
            return false;
        }

        Optional<UserActivity> activityOpt = userActivityRepository.findByUserIdAndSubscriptionId(userId, sub.getSubscriptionId());
        UserActivity activity = activityOpt.orElseGet(() -> {
            initializeUserActivity(userId, sub.getSubscriptionId());
            return userActivityRepository.findByUserIdAndSubscriptionId(userId, sub.getSubscriptionId()).get();
        });

        Optional<SubscriptionConfig> configOpt = subscriptionConfigRepository.findByPlanId(sub.getPlanId());
        if (!configOpt.isPresent()) {
            logger.error("No config found for plan {}", sub.getPlanId());
            return false;
        }

        SubscriptionConfig config = configOpt.get();
        if (activity.getVisitedProfileIds().size() >= config.getMaxProfileVisits()) {
            logger.warn("Profile visit limit reached for user {}", userId);
            return false;
        }

        if (!activity.getVisitedProfileIds().contains(profileId)) {
            activity.getVisitedProfileIds().add(profileId);
            activity.setProfileVisitCount(activity.getVisitedProfileIds().size());
            activity.setLastVisitTimestamp(LocalDateTime.now());
            activity.setUpdatedAt(LocalDateTime.now());
            userActivityRepository.save(activity);
            logger.info("Logged unique profile visit for user {} on profile {}", userId, profileId);
        } else {
            logger.info("Profile {} already visited by user {}, not incrementing count", profileId, userId);
        }
        return true;
    }

    public Map<String, Object> getVisitStats(String userId) {
        Subscriptions sub = subscriptionService.getCurrentSubscription(userId);
        if (sub == null) {
            return Map.of("profileVisitCount", 0, "visitDurationDays", 0);
        }

        Optional<UserActivity> activityOpt = userActivityRepository.findByUserIdAndSubscriptionId(userId, sub.getSubscriptionId());
        if (!activityOpt.isPresent()) {
            return Map.of("profileVisitCount", 0, "visitDurationDays", 0);
        }

        UserActivity activity = activityOpt.get();
        long durationDays = sub.getStartDate() != null && activity.getLastVisitTimestamp() != null
                ? ChronoUnit.DAYS.between(sub.getStartDate(), activity.getLastVisitTimestamp())
                : 0;

        Map<String, Object> stats = new HashMap<>();
        stats.put("profileVisitCount", activity.getProfileVisitCount());
        stats.put("visitDurationDays", durationDays);
        return stats;
    }

    public boolean restrictContactDetails(String userId) {
        Subscriptions sub = subscriptionService.getCurrentSubscription(userId);
        return sub == null || !"active".equalsIgnoreCase(sub.getStatus()) ||
                (sub.getEndDate() != null && sub.getEndDate().isBefore(LocalDateTime.now()));
    }

    public String checkSubscription(String userId) {
        Subscriptions sub = subscriptionService.getCurrentSubscription(userId);
        return sub != null ? sub.getStatus() : "none";
    }

    public boolean incrementMessageCount(String userId, String recipientUserId) {
        Subscriptions sub = subscriptionService.getCurrentSubscription(userId);
        if (sub == null || !"active".equalsIgnoreCase(sub.getStatus())) {
            logger.warn("No active subscription for user {} to send message", userId);
            return false;
        }

        Optional<UserActivity> activityOpt = userActivityRepository.findByUserIdAndSubscriptionId(userId, sub.getSubscriptionId());
        UserActivity activity = activityOpt.orElseGet(() -> {
            initializeUserActivity(userId, sub.getSubscriptionId());
            return userActivityRepository.findByUserIdAndSubscriptionId(userId, sub.getSubscriptionId()).get();
        });

        Optional<SubscriptionConfig> configOpt = subscriptionConfigRepository.findByPlanId(sub.getPlanId());
        if (!configOpt.isPresent()) {
            logger.error("No config found for plan {}", sub.getPlanId());
            return false;
        }

        SubscriptionConfig config = configOpt.get();
        if (activity.getMessagedUserIds().size() >= config.getMaxMessages()) {
            logger.warn("Message limit reached for user {}", userId);
            return false;
        }

        if (!activity.getMessagedUserIds().contains(recipientUserId)) {
            activity.getMessagedUserIds().add(recipientUserId);
            activity.setMessageCount(activity.getMessagedUserIds().size());
            activity.setUpdatedAt(LocalDateTime.now());
            userActivityRepository.save(activity);
            logger.info("Logged message from user {} to recipient {}", userId, recipientUserId);
        } else {
            logger.info("Message already sent to user {} by {}, not incrementing count", recipientUserId, userId);
        }
        return true;
    }

    public Map<String, Integer> getActivityLimits(String userId) {
        Subscriptions sub = subscriptionService.getCurrentSubscription(userId);
        Map<String, Integer> limits = new HashMap<>();
        limits.put("remainingProfileVisits", 0);
        limits.put("remainingMessages", 0);

        if (sub == null || !"active".equalsIgnoreCase(sub.getStatus())) {
            return limits;
        }

        Optional<SubscriptionConfig> configOpt = subscriptionConfigRepository.findByPlanId(sub.getPlanId());
        if (!configOpt.isPresent()) {
            return limits;
        }

        SubscriptionConfig config = configOpt.get();
        Optional<UserActivity> activityOpt = userActivityRepository.findByUserIdAndSubscriptionId(userId, sub.getSubscriptionId());
        UserActivity activity = activityOpt.orElseGet(() -> new UserActivity());

        limits.put("remainingProfileVisits", config.getMaxProfileVisits() - activity.getVisitedProfileIds().size());
        limits.put("remainingMessages", config.getMaxMessages() - activity.getMessagedUserIds().size());
        return limits;
    }

    public String getUserBadge(String userId) {
        Subscriptions sub = subscriptionService.getCurrentSubscription(userId);
        if (sub == null) {
            return "Basic";
        }

        return subscriptionPlanService.getPlanById(sub.getPlanId()).getBadge();
    }
}