package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.SubscriptionConfig;
import com.sparkmind.sparkmate.model.SubscriptionPlan;
import com.sparkmind.sparkmate.repository.SubscriptionConfigRepository;
import com.sparkmind.sparkmate.repository.SubscriptionPlanRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Service
public class SubscriptionPlanService {

    private static final Logger logger = LoggerFactory.getLogger(SubscriptionPlanService.class);
    private static final List<String> DEFAULT_FEATURES = Arrays.asList(
            "View 4X Matches",
            "View advanced search results",
            "Get recommendations"
    );

    @Autowired
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Autowired
    private SubscriptionConfigRepository subscriptionConfigRepository;

    @PostConstruct
    public void initializeBasicPlan() {
        if (subscriptionPlanRepository.findByPlanName("Basic").isEmpty()) {
            SubscriptionPlan basicPlan = new SubscriptionPlan();
            basicPlan.setPlanId(UUID.randomUUID().toString());
            basicPlan.setPlanName("Basic");
            basicPlan.setDescription("Free plan with limited features");
            basicPlan.setMonthlyAmount(0.0);
            basicPlan.setAnnualAmount(0.0);
            basicPlan.setFeatures(Arrays.asList("View 5 profiles"));
            basicPlan.setBadge("Basic");
            basicPlan.setCreatedAt(LocalDateTime.now());
            basicPlan.setLastUpdated(LocalDateTime.now());
            subscriptionPlanRepository.save(basicPlan);

            SubscriptionConfig basicConfig = new SubscriptionConfig();
            basicConfig.setPlanId(basicPlan.getPlanId());
            basicConfig.setMaxProfileVisits(5);
            basicConfig.setMaxMessages(0);
            basicConfig.setDurationDays(36500); // ~100 years for free plan
            subscriptionConfigRepository.save(basicConfig);

            logger.info("Created default Basic plan with ID {}", basicPlan.getPlanId());
        }
    }

    public SubscriptionPlan createPlan(SubscriptionPlan plan) {
        if (subscriptionPlanRepository.findByPlanName(plan.getPlanName()).isPresent()) {
            logger.error("Plan name {} already exists", plan.getPlanName());
            throw new RuntimeException("Plan name already exists");
        }
        plan.setPlanId(UUID.randomUUID().toString());
        plan.setCreatedAt(LocalDateTime.now());
        plan.setLastUpdated(LocalDateTime.now());
        if (plan.getFeatures() == null || plan.getFeatures().isEmpty()) {
            plan.setFeatures(DEFAULT_FEATURES);
        } else {
            plan.getFeatures().addAll(0, DEFAULT_FEATURES);
        }
        if (plan.getBadge() == null) {
            plan.setBadge(plan.getPlanName()); // Default badge to plan name
        }
        SubscriptionPlan savedPlan = subscriptionPlanRepository.save(plan);

        SubscriptionConfig config = new SubscriptionConfig();
        config.setPlanId(savedPlan.getPlanId());
        config.setMaxProfileVisits(50); // Default values, adjustable by admin
        config.setMaxMessages(100);
        config.setDurationDays(30);
        subscriptionConfigRepository.save(config);

        logger.info("Created plan: {}", plan.getPlanName());
        return savedPlan;
    }

    public SubscriptionPlan getPlanById(String planId) {
        return subscriptionPlanRepository.findById(planId)
                .orElseThrow(() -> {
                    logger.error("Plan not found with ID: {}", planId);
                    return new RuntimeException("Plan not found");
                });
    }

    public List<SubscriptionPlan> getAllPlans() {
        logger.info("Fetching all active plans");
        return subscriptionPlanRepository.findByActiveTrue();
    }

    public SubscriptionPlan updatePlan(String planId, SubscriptionPlan updatedPlan) {
        SubscriptionPlan existingPlan = getPlanById(planId);
        existingPlan.setPlanName(updatedPlan.getPlanName());
        existingPlan.setDescription(updatedPlan.getDescription());
        existingPlan.setMonthlyAmount(updatedPlan.getMonthlyAmount());
        existingPlan.setAnnualAmount(updatedPlan.getAnnualAmount());
        existingPlan.setDiscountPercentage(updatedPlan.getDiscountPercentage());
        existingPlan.setPeriod(updatedPlan.getPeriod());
        existingPlan.setInterval(updatedPlan.getInterval());
        if (updatedPlan.getFeatures() == null || updatedPlan.getFeatures().isEmpty()) {
            existingPlan.setFeatures(DEFAULT_FEATURES);
        } else {
            existingPlan.setFeatures(updatedPlan.getFeatures());
            if (!existingPlan.getFeatures().containsAll(DEFAULT_FEATURES)) {
                existingPlan.getFeatures().addAll(0, DEFAULT_FEATURES);
            }
        }
        existingPlan.setBadge(updatedPlan.getBadge() != null ? updatedPlan.getBadge() : existingPlan.getPlanName());
        existingPlan.setLastUpdated(LocalDateTime.now());
        logger.info("Updating plan: {}", existingPlan.getPlanName());
        return subscriptionPlanRepository.save(existingPlan);
    }

    public void deletePlan(String planId) {
        SubscriptionPlan plan = getPlanById(planId);
        plan.setActive(false);
        subscriptionPlanRepository.save(plan);
        logger.info("Plan deleted (soft): {}", plan.getPlanName());
    }
}