package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.model.SubscriptionConfig;
import com.sparkmind.sparkmate.model.Subscriptions;
import com.sparkmind.sparkmate.model.SubscriptionPlan;
import com.sparkmind.sparkmate.model.User;
import com.sparkmind.sparkmate.model.UserActivity;
import com.sparkmind.sparkmate.repository.SubscriptionConfigRepository;
import com.sparkmind.sparkmate.repository.SubscriptionsRepository;
import com.sparkmind.sparkmate.repository.UserActivityRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Service
public class SubscriptionService {

    private static final Logger logger = LoggerFactory.getLogger(SubscriptionService.class);

    @Autowired
    private SubscriptionsRepository subscriptionsRepository;

    @Autowired
    @Lazy
    private UserService userService;

    @Autowired
    private SubscriptionPlanService subscriptionPlanService;

    @Autowired
    private CashfreeService cashfreeService; // Replaced RazorpayService

    @Autowired
    private UserActivityRepository userActivityRepository;

    @Autowired
    private SubscriptionConfigRepository subscriptionConfigRepository;

    private void initializeUserActivity(String userId, String subscriptionId) {
        UserActivity activity = new UserActivity();
        activity.setUserId(userId);
        activity.setSubscriptionId(subscriptionId);
        activity.setProfileVisitCount(0);
        activity.setVisitedProfileIds(new HashSet<>());
        activity.setMessageCount(0);
        activity.setMessagedUserIds(new HashSet<>());
        activity.setCreatedAt(LocalDateTime.now());
        activity.setUpdatedAt(LocalDateTime.now());
        userActivityRepository.save(activity);
        logger.info("Initialized UserActivity for user {} and subscription {}", userId, subscriptionId);
    }

    public Subscriptions createSubscription(String userId, String planId) {
        userService.getUserById(userId).orElseThrow(() -> {
            logger.error("User {} does not exist", userId);
            return new IllegalArgumentException("User does not exist");
        });

        Subscriptions subscription = new Subscriptions();
        subscription.setSubscriptionId(UUID.randomUUID().toString());
        subscription.setUserId(userId);
        subscription.setPlanId(planId);
        subscription.setStatus("active");
        subscription.setStartDate(LocalDateTime.now());
        subscription.setCreatedAt(LocalDateTime.now());
        subscription.setUpdatedAt(LocalDateTime.now());

        Subscriptions saved = subscriptionsRepository.save(subscription);
        initializeUserActivity(userId, saved.getSubscriptionId());
        logger.info("Created subscription {} for user {}", saved.getSubscriptionId(), userId);
        return saved;
    }

    public Map<String, Object> initiateSubscription(String userId, String planId) {
        User user = userService.getUserById(userId).orElseThrow(() -> {
            logger.error("User {} does not exist", userId);
            return new IllegalArgumentException("User does not exist");
        });
        SubscriptionPlan plan = subscriptionPlanService.getPlanById(planId);

        Subscriptions sub = new Subscriptions();
        sub.setSubscriptionId(UUID.randomUUID().toString());
        sub.setUserId(userId);
        sub.setPlanId(planId);
        sub.setStartDate(LocalDateTime.now());
        sub.setStatus("pending");
        sub.setCreatedAt(LocalDateTime.now());
        sub.setUpdatedAt(LocalDateTime.now());

        Subscriptions savedSub = subscriptionsRepository.save(sub);
        userService.addSubscriptionToUser(userId, savedSub.getSubscriptionId());
        initializeUserActivity(userId, savedSub.getSubscriptionId());

        // Create Cashfree order
        Map<String, Object> orderResponse = cashfreeService.createOrder(savedSub, user.getEmail(), user.getMobile());
        logger.info("Initiated subscription {} for user {} with plan {}", savedSub.getSubscriptionId(), userId, planId);
        return orderResponse; // Return order details including payment_session_id
    }

    public Subscriptions updateSubscriptionPayment(String subscriptionId, String transactionId, String paymentStatus) {
        Subscriptions sub = subscriptionsRepository.findById(subscriptionId)
                .orElseThrow(() -> {
                    logger.error("Subscription not found with ID: {}", subscriptionId);
                    return new RuntimeException("Subscription not found");
                });

        // Verify order with Cashfree
        Map<String, Object> orderDetails = cashfreeService.verifyOrder(subscriptionId);
        String orderStatus = (String) orderDetails.get("order_status");

        sub.setTransactionId(transactionId);
        sub.setPaymentStatus(paymentStatus);
        if ("PAID".equalsIgnoreCase(orderStatus)) {
            sub.setStatus("active");
            Optional<SubscriptionConfig> configOpt = subscriptionConfigRepository.findByPlanId(sub.getPlanId());
            int durationDays = configOpt.map(SubscriptionConfig::getDurationDays).orElse(30);
            sub.setEndDate(LocalDateTime.now().plusDays(durationDays));
        } else {
            sub.setStatus("failed");
            sub.setEndDate(LocalDateTime.now());
        }
        sub.setUpdatedAt(LocalDateTime.now());
        Subscriptions saved = subscriptionsRepository.save(sub);
        logger.info("Updated payment for subscription {} with status {}", subscriptionId, paymentStatus);
        return saved;
    }

    public Subscriptions getCurrentSubscription(String userId) {
        return subscriptionsRepository.findByUserIdAndStatus(userId, "active")
                .filter(sub -> sub.getEndDate() == null || sub.getEndDate().isAfter(LocalDateTime.now()))
                .orElse(null);
    }

    public Subscriptions activateBasicPlan(String userId, String planId) {
        userService.getUserById(userId).orElseThrow(() -> {
            logger.error("User {} does not exist", userId);
            return new IllegalArgumentException("User does not exist");
        });

        SubscriptionPlan plan = subscriptionPlanService.getPlanById(planId);
        if (!"Basic".equalsIgnoreCase(plan.getPlanName())) {
            logger.error("Plan {} is not a Basic plan", planId);
            throw new IllegalArgumentException("Invalid plan for basic activation");
        }

        Subscriptions existingSub = getCurrentSubscription(userId);
        if (existingSub != null && "active".equalsIgnoreCase(existingSub.getStatus())) {
            logger.warn("User {} already has an active subscription {}", userId, existingSub.getSubscriptionId());
            throw new IllegalStateException("User already has an active subscription");
        }

        Subscriptions subscription = new Subscriptions();
        subscription.setSubscriptionId(UUID.randomUUID().toString());
        subscription.setUserId(userId);
        subscription.setPlanId(planId);
        subscription.setStatus("active");
        subscription.setStartDate(LocalDateTime.now());
        Optional<SubscriptionConfig> configOpt = subscriptionConfigRepository.findByPlanId(planId);
        int durationDays = configOpt.map(SubscriptionConfig::getDurationDays).orElse(30);
        subscription.setEndDate(LocalDateTime.now().plusDays(durationDays));
        subscription.setCreatedAt(LocalDateTime.now());
        subscription.setUpdatedAt(LocalDateTime.now());

        Subscriptions saved = subscriptionsRepository.save(subscription);
        initializeUserActivity(userId, saved.getSubscriptionId());
        logger.info("Activated Basic plan subscription {} for user {}", saved.getSubscriptionId(), userId);
        return saved;
    }
}