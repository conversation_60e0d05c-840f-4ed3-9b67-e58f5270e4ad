package com.sparkmind.sparkmate.service;

import com.sparkmind.sparkmate.dto.UserProfileDTO;
import com.sparkmind.sparkmate.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserProfileService {
    private static final Logger logger = LoggerFactory.getLogger(UserProfileService.class);

    @Autowired
    private UserService userService;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private ProfileInfoService profileInfoService;

    @Autowired
    private ProfileImagesService profileImagesService;

    public List<UserProfileDTO> getFilteredUserProfiles() {
        // Get the logged-in user
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String loggedInUserEmail = auth.getName(); // Assuming email is the principal
        User loggedInUser = userService.findByEmail(loggedInUserEmail)
                .orElseThrow(() -> new RuntimeException("Logged-in user not found: " + loggedInUserEmail));

        String lookingFor = loggedInUser.getLookingFor(); // "bride" or "groom"
        logger.info("Logged-in user {} is looking for: {}", loggedInUserEmail, lookingFor);

        // Fetch all users and filter based on lookingFor
        List<User> allUsers = userService.getAllUsers();
        return allUsers.stream()
                .filter(user -> !user.getId().equals(loggedInUser.getId())) // Exclude logged-in user
                .map(user -> {
                    UserProfileDTO dto = new UserProfileDTO();
                    dto.setUserId(user.getId());
                    dto.setEmail(user.getEmail());
                    dto.setMobile(user.getMobile());
                    dto.setLookingFor(user.getLookingFor());

                    // Fetch Profile
                    profileService.getProfileByUserId(user.getId()).ifPresent(profile -> {
                        dto.setProfileId(profile.getProfileId());
                        dto.setActive(profile.getActive());
                        dto.setLastSeen(profile.getLastSeen());

                        // Fetch ProfileInfo and check gender
                        profileInfoService.getProfileInfoByProfileId(profile.getProfileId()).ifPresent(info -> {
                            String gender = info.getGender();
                            // Only set fields if gender matches lookingFor
                            if (("bride".equalsIgnoreCase(lookingFor) && "female".equalsIgnoreCase(gender)) ||
                                    ("groom".equalsIgnoreCase(lookingFor) && "male".equalsIgnoreCase(gender))) {
                                dto.setFirstName(info.getFirstName());
                                dto.setLastName(info.getLastName());
                                String combinedName = (info.getFirstName() != null ? info.getFirstName() : "") + " " +
                                        (info.getLastName() != null ? info.getLastName() : "");
                                dto.setName(combinedName.trim());
                                dto.setGender(gender);
                                dto.setDob(info.getDob());
                                dto.setReligion(info.getReligion());
                                dto.setCaste(info.getCaste());
                                dto.setMaritalStatus(info.getMaritalStatus());
                                dto.setDescription(info.getDescription());
                            }
                        });

                        // Fetch ProfileImages (first image)
                        profileImagesService.getProfileImagesByProfileId(profile.getProfileId()).ifPresent(images -> {
                            if (!images.getImages().isEmpty()) {
                                dto.setProfileImage(images.getImages().get(0));
                            }
                        });
                    });

                    // Only include if name is set (i.e., gender matched)
                    return dto.getName() != null ? dto : null;
                })
                .filter(dto -> dto != null) // Remove null entries from gender mismatch
                .collect(Collectors.toList());
    }

    public UserProfileDTO getUserProfileByProfileId(String profileId) {
        return profileService.getProfileByProfileId(profileId).map(profile -> {
            UserProfileDTO dto = new UserProfileDTO();
            dto.setProfileId(profile.getProfileId());
            dto.setActive(profile.getActive());
            dto.setLastSeen(profile.getLastSeen());

            userService.getUserById(profile.getUserId()).ifPresent(user -> {
                dto.setUserId(user.getId());
                dto.setEmail(user.getEmail());
                dto.setMobile(user.getMobile());
                dto.setLookingFor(user.getLookingFor());
            });

            profileInfoService.getProfileInfoByProfileId(profileId).ifPresent(info -> {
                dto.setFirstName(info.getFirstName());
                dto.setLastName(info.getLastName());
                String combinedName = (info.getFirstName() != null ? info.getFirstName() : "") + " " +
                        (info.getLastName() != null ? info.getLastName() : "");
                dto.setName(combinedName.trim());
                dto.setGender(info.getGender());
                dto.setDob(info.getDob());
                dto.setReligion(info.getReligion());
                dto.setCaste(info.getCaste());
                dto.setMaritalStatus(info.getMaritalStatus());
                dto.setDescription(info.getDescription());
            });

            profileImagesService.getProfileImagesByProfileId(profileId).ifPresent(images -> {
                if (!images.getImages().isEmpty()) {
                    dto.setProfileImage(images.getImages().get(0));
                }
            });

            return dto;
        }).orElse(null);
    }
}