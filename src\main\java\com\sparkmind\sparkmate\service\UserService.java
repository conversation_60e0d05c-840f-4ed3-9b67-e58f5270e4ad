package com.sparkmind.sparkmate.service;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.UserRecord;
import com.razorpay.RazorpayException;
import com.sparkmind.sparkmate.model.SubscriptionPlan;
import com.sparkmind.sparkmate.model.Subscriptions;
import com.sparkmind.sparkmate.model.User;
import com.sparkmind.sparkmate.repository.SubscriptionPlanRepository;
import com.sparkmind.sparkmate.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Service
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ProfileService profileService;

    @Autowired
    private SubscriptionManagerService subscriptionManagerService;

    @Autowired
    private SubscriptionService subscriptionService;

    @Autowired
    private SubscriptionPlanRepository subscriptionPlanRepository;



    public User createUser(User user) {
        logger.info("Creating user with ID {}", user.getId());
        User savedUser = userRepository.save(user);
        // Automatically assign Basic plan
        Optional<SubscriptionPlan> basicPlanOpt = subscriptionPlanRepository.findByPlanName("Basic");
        if (basicPlanOpt.isPresent()) {
            String planId = basicPlanOpt.get().getPlanId();
            subscriptionService.initiateSubscription(user.getId(), planId);
            logger.info("Assigned Basic plan to user {}", user.getId());
        } else {
            logger.error("Basic plan not found for user {}", user.getId());
        }
        return savedUser;
    }

    public List<User> getAllUsers() {
        logger.info("Fetching all users");
        return userRepository.findAll();
    }


    public Optional<User> getUserById(String id) {
        logger.info("Fetching user with id: {}", id);
        return userRepository.findById(id);
    }

    public Optional<User> findByEmail(String email) {
        logger.info("Finding user by email: {}", email);
        return userRepository.findByEmail(email);
    }

    public boolean isAdmin(String userId) {
        return getUserById(userId)
                .map(user -> "admin".equalsIgnoreCase(user.getRole()))
                .orElse(false);
    }

    public void syncUserFromFirestore(User user) {
        Optional<User> existingUserOpt = userRepository.findById(user.getId());
        if (existingUserOpt.isPresent()) {
            User existingUser = existingUserOpt.get();
            if (isUserUnchanged(existingUser, user)) {
                logger.info("User {} already synced and unchanged, skipping.", user.getId());
                return;
            }
            logger.info("User {} exists but data changed, updating.", user.getId());
            existingUser.setName(user.getName() != null ? user.getName() : existingUser.getName());
            existingUser.setEmail(user.getEmail());
            existingUser.setMobile(user.getMobile());
            userRepository.save(existingUser);
            return;
        }

        logger.info("New user {} detected, syncing.", user.getId());
        User newUser = new User();
        newUser.setId(user.getId());
        newUser.setName(user.getName() != null ? user.getName() : "Unnamed");
        newUser.setEmail(user.getEmail());
        newUser.setMobile(user.getMobile());
        newUser.setRole(user.getRole() != null ? user.getRole() : "customer");
        newUser.setStatus(user.getStatus() != null ? user.getStatus() : "Active");
        newUser.setLookingFor(user.getLookingFor());
        newUser.setAdditionalInfo(user.getAdditionalInfo());
        newUser.setIsMarriageBureau(user.getIsMarriageBureau());
        userRepository.save(newUser);

        profileService.createProfile(newUser.getId());

    }

    public void syncMarriageBureauUser(User user) {
        Optional<User> existingUserOpt = userRepository.findById(user.getId());
        if (existingUserOpt.isPresent()) {
            User existingUser = existingUserOpt.get();
            if (isUserUnchanged(existingUser, user)) {
                logger.info("Marriage bureau user {} already synced and unchanged, skipping.", user.getId());
                return;
            }
            logger.info("Marriage bureau user {} exists but data changed, updating.", user.getId());
            existingUser.setName(user.getName() != null ? user.getName() : existingUser.getName());
            existingUser.setEmail(user.getEmail());
            existingUser.setMobile(user.getMobile());
            existingUser.setIsMarriageBureau(true); // Explicitly set to true
            userRepository.save(existingUser);
            return;
        }

        logger.info("New marriage bureau user {} detected, syncing.", user.getId());
        User newUser = new User();
        newUser.setId(user.getId());
        newUser.setName(user.getName() != null ? user.getName() : "Unnamed");
        newUser.setEmail(user.getEmail());
        newUser.setMobile(user.getMobile());
        newUser.setRole(user.getRole() != null ? user.getRole() : "customer");
        newUser.setStatus(user.getStatus() != null ? user.getStatus() : "Active");
        newUser.setLookingFor(user.getLookingFor());
        newUser.setAdditionalInfo(user.getAdditionalInfo());
        newUser.setIsMarriageBureau(true); // Explicitly set to true
        userRepository.save(newUser);

        profileService.createProfile(newUser.getId());
    }

    public User updateUser(String id, User user) {
        Optional<User> existingOpt = userRepository.findById(id);
        if (!existingOpt.isPresent()) {
            logger.warn("User not found with id: {}", id);
            throw new RuntimeException("User not found with id: " + id);
        }
        user.setId(id);
        User updatedUser = userRepository.save(user);

        try {
            UserRecord.UpdateRequest request = new UserRecord.UpdateRequest(id)
                    .setEmail(user.getEmail())
                    .setDisplayName(user.getName())
                    .setPhoneNumber(user.getMobile());
            FirebaseAuth.getInstance().updateUser(request);
            logger.info("User {} synced with Firebase", id);
        } catch (Exception e) {
            logger.error("Failed to sync with Firebase for user {}: {}", id, e.getMessage());
            throw new RuntimeException("Failed to sync with Firebase: " + e.getMessage());
        }
        return updatedUser;
    }

    public void deleteUser(String id) {
        if (!userRepository.existsById(id)) {
            logger.warn("User not found with id: {}", id);
            throw new RuntimeException("User not found with id: " + id);
        }
        userRepository.deleteById(id);
        logger.info("User deleted with id: {}", id);
    }

    private boolean isUserUnchanged(User dbUser, User firestoreUser) {
        return Objects.equals(dbUser.getName(), firestoreUser.getName()) &&
                Objects.equals(dbUser.getEmail(), firestoreUser.getEmail()) &&
                Objects.equals(dbUser.getMobile(), firestoreUser.getMobile()) &&
                Objects.equals(dbUser.getRole(), firestoreUser.getRole()) &&
                Objects.equals(dbUser.getStatus(), firestoreUser.getStatus()) &&
                Objects.equals(dbUser.getLookingFor(), firestoreUser.getLookingFor()) &&
                Objects.equals(dbUser.getAdditionalInfo(), firestoreUser.getAdditionalInfo());
    }

    public void addSubscriptionToUser(String userId, String subscriptionId) {
        Optional<User> userOpt = getUserById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            // Assuming User has a subscriptions field (e.g., List<String>)
            if (user.getSubscriptions() == null) {
                user.setSubscriptions(new java.util.ArrayList<>());
            }
            user.getSubscriptions().add(subscriptionId);
            userRepository.save(user);
            logger.info("Added subscription {} to user {}", subscriptionId, userId);
        } else {
            logger.error("User {} not found for adding subscription {}", userId, subscriptionId);
            throw new IllegalArgumentException("User not found");
        }
    }


    public List<User> findByStatus(String status, Pageable pageable) {
        logger.info("Fetching users with status: {}", status);
        return userRepository.findByStatus(status, pageable);
    }

    public List<User> filterByStatus(String status) {
        return userRepository.filterByStatus(status);
    }
}