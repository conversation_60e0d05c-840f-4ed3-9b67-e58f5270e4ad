# Global configuration
spring:
  application:
    name: sparkmate
  redis:
    host: localhost
    port: 6379
    database: 0

server:
  port: 8080

logging.level.com.google=DEBUG

razorpay:
  key_id: rzp_test_LfPe7hfNWVCzoQ
  key_secret: xxxxxxxxxxxxxxxxxxxx


cashfree.app-id=TEST1067436635e7859a0e3876f6e38766347601
cashfree.secret-key=cfsk_ma_test_5a534b1896b2046974420e8502eb66c0_bd087607
cashfree.api-version=2023-08-01
cashfree.environment=sandbox
cashfree.return-url=https://sparkmate-frontend-272707940557.us-central1.run.app//subscriptions


gcs.bucket.name=sparkmate-89506
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    disable-swagger-default-url: true
  api-docs:
    path: /api-docs
    enabled: true
  packages-to-scan: com.sparkmind.sparkmate
  paths-to-match: /api/**

logging:
  level:
    root: INFO
    com.sparkmind.sparkmate: DEBUG





# MongoDB configuration
mongo.uri=mongodb+srv://bharativikas1203:<EMAIL>/sparkmate2?retryWrites=true&w=majority
mongo.database=sparkmate2

# Allowed origins for CORS
cors.allowed.origins=http://localhost:8080,http://localhost:5173,https://sparkmate-frontend-272707940557.us-central1.run.app ,"https://*.ngrok-free.app",